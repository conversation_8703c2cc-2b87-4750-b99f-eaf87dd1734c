{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\.json)?[\\/#\\?]?$", "originalSource": "/api/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/admin(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\.json)?[\\/#\\?]?$", "originalSource": "/admin/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "BW8ZKHfqzOk1l4_dq9hU2", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "pC+gLU7N0gPM+hL33F4gA5grgGqY36LSmSskWwyj33U=", "__NEXT_PREVIEW_MODE_ID": "73cd047b8b594abb2b742c6820b276a5", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "1ae26338d04b2bd4e11edbaa9194d1d2466561083c4126e2766ac5d2305f03e1", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "ca78a8069d7a513be7a9eb390deecb0dc84032a30bc57a68796d1c8976c9a71a"}}}, "functions": {}, "sortedMiddleware": ["/"]}