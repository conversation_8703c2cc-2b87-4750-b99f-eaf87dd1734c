"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[102],{20102:(e,t,n)=>{n.d(t,{default:()=>l});var a=n(12115),r=n(35695);let o=e=>{let t="https://bakasana-travel.blog";return[{rel:"alternate",hreflang:"pl",href:`${t}${e}`},{rel:"alternate",hreflang:"en",href:`${t}${e}?lang=en`},{rel:"alternate",hreflang:"x-default",href:`${t}${e}`}]};function l(e){let{title:t,description:n,keywords:l,structuredData:c,canonicalUrl:m,noIndex:d=!1,imageUrl:i}=e,p=(0,r.usePathname)();return(0,a.useEffect)(()=>{if(t&&(document.title=t),n){let e=document.querySelector('meta[name="description"]');e||((e=document.createElement("meta")).name="description",document.head.appendChild(e)),e.content=n}if(l){let e=document.querySelector('meta[name="keywords"]');e||((e=document.createElement("meta")).name="keywords",document.head.appendChild(e)),e.content=l}if(m){let e=document.querySelector('link[rel="canonical"]');e||((e=document.createElement("link")).rel="canonical",document.head.appendChild(e)),e.href=m}if(o(p).forEach(e=>{let t=document.querySelector(`link[hreflang="${e.hreflang}"]`);t||((t=document.createElement("link")).rel="alternate",t.hreflang=e.hreflang,document.head.appendChild(t)),t.href=e.href}),d){let e=document.querySelector('meta[name="robots"]');e||((e=document.createElement("meta")).name="robots",document.head.appendChild(e)),e.content="noindex, nofollow"}let e=[{property:"og:title",content:t},{property:"og:description",content:n},{property:"og:url",content:m},{property:"og:type",content:"website"},{property:"og:locale",content:"pl_PL"},{property:"og:site_name",content:"BAKASANA - Retreaty Jogi"}];i&&e.push({property:"og:image",content:i},{property:"og:image:width",content:"1200"},{property:"og:image:height",content:"630"},{property:"og:image:alt",content:t}),e.forEach(e=>{let t=document.querySelector(`meta[property="${e.property}"]`);t||((t=document.createElement("meta")).setAttribute("property",e.property),document.head.appendChild(t)),t.content=e.content});let a=[{name:"twitter:card",content:"summary_large_image"},{name:"twitter:site",content:"@bakasana_yoga"},{name:"twitter:creator",content:"@bakasana_yoga"},{name:"twitter:title",content:t},{name:"twitter:description",content:(null==n?void 0:n.substring(0,157))+"..."}];if(i&&a.push({name:"twitter:image",content:i}),a.forEach(e=>{let t=document.querySelector(`meta[name="${e.name}"]`);t||((t=document.createElement("meta")).name=e.name,document.head.appendChild(t)),t.content=e.content}),c){let e=document.querySelector("#structured-data-script");e||((e=document.createElement("script")).id="structured-data-script",e.type="application/ld+json",document.head.appendChild(e)),e.innerHTML=JSON.stringify(c)}window.gtag&&window.gtag("event","seo_optimization",{event_category:"SEO",event_label:p,custom_parameter_1:(null==t?void 0:t.length)||0,custom_parameter_2:(null==n?void 0:n.length)||0})},[t,n,l,m,d,i,c,p]),null}}}]);