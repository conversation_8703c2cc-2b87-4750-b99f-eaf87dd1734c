(()=>{var e={};e.id=5596,e.ids=[5596],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17117:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>p,tree:()=>c});var s=t(65239),r=t(48088),i=t(88170),n=t.n(i),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(a,l);let c={children:["",{children:["transformacyjne-podroze-azja",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,98325)),"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\transformacyjne-podroze-azja\\page.jsx"]}]},{metadata:{icon:[],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,47103))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,39769)),"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\layout.jsx"],error:[()=>Promise.resolve().then(t.bind(t,42201)),"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\error.jsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,43867)),"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\not-found.jsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,47103))).default(e)],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\transformacyjne-podroze-azja\\page.jsx"],m={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/transformacyjne-podroze-azja/page",pathname:"/transformacyjne-podroze-azja",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21546:(e,a,t)=>{Promise.resolve().then(t.t.bind(t,4536,23)),Promise.resolve().then(t.t.bind(t,49603,23)),Promise.resolve().then(t.bind(t,86810)),Promise.resolve().then(t.bind(t,77691))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},56074:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(26373).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},62820:(e,a,t)=>{"use strict";t.d(a,{Badge:()=>o});var s=t(60687);t(43210);var r=t(24224),i=t(65222);let n=(0,r.F)("inline-flex items-center border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:a,...t}){return(0,s.jsx)("div",{className:(0,i.cn)(n({variant:a}),e),...t})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66007:(e,a,t)=>{"use strict";t.d(a,{Zp:()=>c,Wu:()=>d});var s=t(37413),r=t(61120),i=t.n(r),n=t(75986),o=t(8974);function l(...e){return(0,o.QP)((0,n.$)(e))}let c=i().forwardRef(({className:e,...a},t)=>(0,s.jsx)("div",{ref:t,className:l("bg-sanctuary shadow-subtle transition-all duration-300 hover:shadow-premium-shadow",e),...a}));c.displayName="Card",i().forwardRef(({className:e,...a},t)=>(0,s.jsx)("div",{ref:t,className:l("flex flex-col space-y-1.5 p-6",e),...a})).displayName="CardHeader",i().forwardRef(({className:e,...a},t)=>(0,s.jsx)("h3",{ref:t,className:l("font-cormorant text-2xl font-light leading-none tracking-tight text-charcoal",e),...a})).displayName="CardTitle",i().forwardRef(({className:e,...a},t)=>(0,s.jsx)("p",{ref:t,className:l("text-sm text-sage leading-relaxed",e),...a})).displayName="CardDescription";let d=i().forwardRef(({className:e,...a},t)=>(0,s.jsx)("div",{ref:t,className:l("p-6 pt-0",e),...a}));d.displayName="CardContent",i().forwardRef(({className:e,...a},t)=>(0,s.jsx)("div",{ref:t,className:l("flex items-center p-6 pt-0",e),...a})).displayName="CardFooter"},67719:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(26373).A)("mountain",[["path",{d:"m8 3 4 8 5-5 5 15H2L8 3z",key:"otkl63"}]])},76717:(e,a,t)=>{"use strict";t.d(a,{Button:()=>c});var s=t(60687),r=t(43210),i=t(81391),n=t(24224),o=t(65222);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap font-light transition-opacity duration-300 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-offset-1 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{primary:"bg-transparent border border-charcoal text-charcoal hover:opacity-70 font-inter text-xs font-light letter-spacing-wide uppercase px-12 py-4",secondary:"bg-transparent border border-stone text-stone hover:opacity-70 font-inter text-xs font-light letter-spacing-wide uppercase px-12 py-4",accent:"bg-transparent border border-temple-gold text-temple-gold hover:opacity-70 font-inter text-xs font-light letter-spacing-wide uppercase px-12 py-4",ghost:"bg-transparent text-charcoal hover:opacity-70 font-inter text-xs font-light letter-spacing-wide uppercase px-12 py-4",outline:"bg-transparent border border-stone/20 text-charcoal hover:opacity-70",hero:"bg-transparent border border-stone/10 text-charcoal hover:opacity-70"},size:{sm:"px-8 py-3 text-xs",default:"px-12 py-4 text-xs",lg:"px-16 py-5 text-sm",icon:"h-9 w-9 px-0"}},defaultVariants:{variant:"primary",size:"default"}}),c=r.forwardRef(({className:e,variant:a,size:t,asChild:r=!1,...n},c)=>{let d=r?i.DX:"button";return(0,s.jsx)(d,{className:(0,o.cn)(l({variant:a,size:t,className:e})),ref:c,...n})});c.displayName="Button"},77691:(e,a,t)=>{"use strict";t.d(a,{Button:()=>r});var s=t(12907);let r=(0,s.registerClientReference)(function(){throw Error("Attempted to call Button() from the server but Button is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\button.jsx","Button");(0,s.registerClientReference)(function(){throw Error("Attempted to call buttonVariants() from the server but buttonVariants is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\button.jsx","buttonVariants")},79551:e=>{"use strict";e.exports=require("url")},84594:(e,a,t)=>{Promise.resolve().then(t.t.bind(t,85814,23)),Promise.resolve().then(t.t.bind(t,46533,23)),Promise.resolve().then(t.bind(t,62820)),Promise.resolve().then(t.bind(t,76717))},86810:(e,a,t)=>{"use strict";t.d(a,{Badge:()=>r});var s=t(12907);let r=(0,s.registerClientReference)(function(){throw Error("Attempted to call Badge() from the server but Badge is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\badge.jsx","Badge");(0,s.registerClientReference)(function(){throw Error("Attempted to call badgeVariants() from the server but badgeVariants is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\badge.jsx","badgeVariants")},98325:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>z,metadata:()=>k});var s=t(37413),r=t(4536),i=t.n(r),n=t(53384),o=t(77691),l=t(66007),c=t(86810),d=t(85838),m=t(26373);let p=(0,m.A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]]),x=(0,m.A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),h=(0,m.A)("flower",[["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}],["path",{d:"M12 16.5A4.5 4.5 0 1 1 7.5 12 4.5 4.5 0 1 1 12 7.5a4.5 4.5 0 1 1 4.5 4.5 4.5 4.5 0 1 1-4.5 4.5",key:"14wa3c"}],["path",{d:"M12 7.5V9",key:"1oy5b0"}],["path",{d:"M7.5 12H9",key:"eltsq1"}],["path",{d:"M16.5 12H15",key:"vk5kw4"}],["path",{d:"M12 16.5V15",key:"k7eayi"}],["path",{d:"m8 8 1.88 1.88",key:"nxy4qf"}],["path",{d:"M14.12 9.88 16 8",key:"1lst6k"}],["path",{d:"m8 16 1.88-1.88",key:"h2eex1"}],["path",{d:"M14.12 14.12 16 16",key:"uqkrx3"}]]),j=(0,m.A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]),b=(0,m.A)("waves",[["path",{d:"M2 6c.6.5 1.2 1 2.5 1C7 7 7 5 9.5 5c2.6 0 2.4 2 5 2 2.5 0 2.5-2 5-2 1.3 0 1.9.5 2.5 1",key:"knzxuh"}],["path",{d:"M2 12c.6.5 1.2 1 2.5 1 2.5 0 2.5-2 5-2 2.6 0 2.4 2 5 2 2.5 0 2.5-2 5-2 1.3 0 1.9.5 2.5 1",key:"2jd2cc"}],["path",{d:"M2 18c.6.5 1.2 1 2.5 1 2.5 0 2.5-2 5-2 2.6 0 2.4 2 5 2 2.5 0 2.5-2 5-2 1.3 0 1.9.5 2.5 1",key:"rd2r6e"}]]);var u=t(67719);let f=(0,m.A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]);var g=t(49972),y=t(56074),w=t(71750),v=t(60343),N=t(56378);let k={title:"Transformacyjne Podr\xf3że Azja 2025 - Duchowe Retreaty Jogi | BAKASANA",description:"✨ Transformacyjne podr\xf3że do Azji 2025 - duchowe retreaty jogi na Bali i Sri Lanka. Odkryj siebie, medytacja, ayurveda, świątynie. Głęboka transformacja z Julią Jakubowicz.",keywords:"transformacyjne podr\xf3że azja, duchowe podr\xf3że azja, transformacja osobista, spiritual journey, retreaty transformacyjne, duchowe wakacje, meditation retreat",openGraph:{title:"Transformacyjne Podr\xf3że Azja 2025 - Duchowe Retreaty | BAKASANA",description:"✨ Transformacyjne podr\xf3że do Azji z duchowymi retreatami jogi. Odkryj siebie na Bali i Sri Lanka. Głęboka transformacja.",images:["/images/og/transformacyjne-podroze-azja-2025.jpg"]},alternates:{canonical:"https://bakasana-travel.blog/transformacyjne-podroze-azja"}},z=()=>{let e=[{icon:(0,s.jsx)(d.A,{className:"w-6 h-6 text-temple"}),title:"Emocjonalne uzdrowienie",description:"Uwolnij się od stresu i negatywnych emocji",color:"bg-pink-100"},{icon:(0,s.jsx)(p,{className:"w-6 h-6 text-temple"}),title:"Duchowe przebudzenie",description:"Połącz się z głębszą częścią siebie",color:"bg-purple-100"},{icon:(0,s.jsx)(x,{className:"w-6 h-6 text-temple"}),title:"Energetyczna odnowa",description:"Odn\xf3w swoją witalność i siłę życiową",color:"bg-yellow-100"},{icon:(0,s.jsx)(h,{className:"w-6 h-6 text-temple"}),title:"Mentalna jasność",description:"Osiągnij spok\xf3j umysłu i koncentrację",color:"bg-blue-100"}],a=[{phase:"Przygotowanie",description:"Intencje, otwarcie serca, pierwszy kontakt z grupą",icon:(0,s.jsx)(j,{className:"w-5 h-5 text-temple"}),days:"Dni 1-2"},{phase:"Oczyszczenie",description:"Detoks ciała i umysłu, uwolnienie od nawyk\xf3w",icon:(0,s.jsx)(b,{className:"w-5 h-5 text-temple"}),days:"Dni 3-5"},{phase:"Odkrywanie",description:"Głęboka praktyka, medytacja, praca z energią",icon:(0,s.jsx)(u.A,{className:"w-5 h-5 text-temple"}),days:"Dni 6-8"},{phase:"Integracja",description:"Połączenie doświadczeń, planowanie przyszłości",icon:(0,s.jsx)(f,{className:"w-5 h-5 text-temple"}),days:"Dni 9-10"}];return(0,s.jsxs)("div",{className:"min-h-screen",children:[(0,s.jsxs)("section",{className:"relative min-h-screen flex items-center justify-center",children:[(0,s.jsx)("div",{className:"absolute inset-0 z-0",children:(0,s.jsx)(n.default,{src:"/images/spiritual/transformation-hero.webp",alt:"Transformacyjne podr\xf3że Azja - duchowa przemiana",fill:!0,className:"object-cover opacity-40",priority:!0})}),(0,s.jsxs)("div",{className:"relative z-10 text-center max-w-4xl mx-auto px-4",children:[(0,s.jsx)(c.Badge,{className:"mb-6 bg-white/90 text-temple border-temple/30 px-6 py-3 text-lg",children:"✨ Transformacyjne Podr\xf3że Azja 2025"}),(0,s.jsxs)("h1",{className:"text-5xl md:text-7xl font-bold text-charcoal mb-8",children:["Odkryj ",(0,s.jsx)("span",{className:"text-temple",children:"Siebie"})," ",(0,s.jsx)("br",{}),"w Sercu Azji"]}),(0,s.jsx)("p",{className:"text-xl md:text-2xl text-wood mb-12 max-w-3xl mx-auto leading-relaxed",children:"Duchowe retreaty jogi na Bali i Sri Lanka. Głęboka transformacja, medytacja, ayurveda, świątynie. Podr\xf3ż do siebie w najpiękniejszych miejscach świata."}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-6 justify-center mb-12",children:[(0,s.jsx)(o.Button,{size:"lg",className:"bg-temple hover:bg-temple/90 text-xl px-12 py-8",asChild:!0,children:(0,s.jsxs)(i(),{href:"/rezerwacja",children:["Rozpocznij Transformację",(0,s.jsx)(p,{className:"w-6 h-6 ml-3"})]})}),(0,s.jsx)(o.Button,{size:"lg",variant:"outline",className:"border-temple text-temple hover:bg-temple/10 text-xl px-12 py-8",asChild:!0,children:(0,s.jsx)(i(),{href:"/program",children:"Odkryj Programy"})})]}),(0,s.jsxs)("div",{className:"flex flex-wrap items-center justify-center gap-8 text-wood",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(g.A,{className:"w-6 h-6 text-golden fill-golden"}),(0,s.jsx)("span",{className:"text-lg",children:"127 transformacji"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(d.A,{className:"w-6 h-6 text-temple"}),(0,s.jsx)("span",{className:"text-lg",children:"10 lat doświadczenia"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(h,{className:"w-6 h-6 text-temple"}),(0,s.jsx)("span",{className:"text-lg",children:"Duchowe przebudzenie"})]})]})]})]}),(0,s.jsx)("section",{className:"py-20 bg-gradient-to-br from-sanctuary to-mist",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4",children:[(0,s.jsxs)("div",{className:"text-center mb-16",children:[(0,s.jsx)("h2",{className:"text-4xl font-bold text-charcoal mb-6",children:"Obszary Transformacji"}),(0,s.jsx)("p",{className:"text-xl text-wood max-w-3xl mx-auto",children:"Nasze duchowe podr\xf3że oddziałują na wszystkie aspekty twojego bytu, prowadząc do głębokiej i trwałej przemiany."})]}),(0,s.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8",children:e.map((e,a)=>(0,s.jsx)(l.Zp,{className:"bg-white/80 backdrop-blur-sm border-temple/20 hover:shadow-xl transition-shadow",children:(0,s.jsxs)(l.Wu,{className:"p-8 text-center",children:[(0,s.jsx)("div",{className:`w-16 h-16 ${e.color} rounded-full flex items-center justify-center mx-auto mb-6`,children:e.icon}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-charcoal mb-4",children:e.title}),(0,s.jsx)("p",{className:"text-wood",children:e.description})]})},a))})]})}),(0,s.jsx)("section",{className:"py-20",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4",children:[(0,s.jsxs)("div",{className:"text-center mb-16",children:[(0,s.jsx)("h2",{className:"text-4xl font-bold text-charcoal mb-6",children:"Ścieżka Transformacji"}),(0,s.jsx)("p",{className:"text-xl text-wood max-w-3xl mx-auto",children:"Każda podr\xf3ż to starannie zaplanowana ścieżka duchowego rozwoju i osobistej przemiany."})]}),(0,s.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8",children:a.map((e,a)=>(0,s.jsx)(l.Zp,{className:"bg-white/80 backdrop-blur-sm border-temple/20 relative",children:(0,s.jsxs)(l.Wu,{className:"p-8",children:[(0,s.jsx)("div",{className:"absolute -top-4 left-8 w-8 h-8 bg-temple rounded-full flex items-center justify-center text-white font-bold",children:a+1}),(0,s.jsx)("div",{className:"w-12 h-12 bg-temple/10 rounded-full flex items-center justify-center mb-6 mt-4",children:e.icon}),(0,s.jsx)(c.Badge,{variant:"secondary",className:"mb-4 bg-temple/10 text-temple",children:e.days}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-charcoal mb-4",children:e.phase}),(0,s.jsx)("p",{className:"text-wood",children:e.description})]})},a))})]})}),(0,s.jsx)("section",{className:"py-20 bg-temple/5",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4",children:[(0,s.jsxs)("div",{className:"text-center mb-16",children:[(0,s.jsx)("h2",{className:"text-4xl font-bold text-charcoal mb-6",children:"Duchowe Praktyki"}),(0,s.jsx)("p",{className:"text-xl text-wood max-w-3xl mx-auto",children:"Autentyczne praktyki duchowe Azji, kt\xf3re otwierają drzwi do głębokiej transformacji i wewnętrznego pokoju."})]}),(0,s.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8",children:[{name:"Sunrise Meditation",description:"Codzienna medytacja o wschodzie słońca",benefit:"Spok\xf3j umysłu",image:"/images/practices/sunrise-meditation.webp"},{name:"Mantra Chanting",description:"Śpiewanie świętych mantry",benefit:"Wibracja i energia",image:"/images/practices/mantra-chanting.webp"},{name:"Chakra Balancing",description:"R\xf3wnoważenie centr\xf3w energetycznych",benefit:"Harmonia wewnętrzna",image:"/images/practices/chakra-balancing.webp"},{name:"Sacred Rituals",description:"Uczestnictwo w lokalnych ceremoniach",benefit:"Głęboka duchowość",image:"/images/practices/sacred-rituals.webp"}].map((e,a)=>(0,s.jsxs)(l.Zp,{className:"bg-white/80 backdrop-blur-sm border-temple/20 overflow-hidden",children:[(0,s.jsx)("div",{className:"relative h-48",children:(0,s.jsx)(n.default,{src:e.image,alt:e.name,fill:!0,className:"object-cover"})}),(0,s.jsxs)(l.Wu,{className:"p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-charcoal mb-3",children:e.name}),(0,s.jsx)("p",{className:"text-wood text-sm mb-4",children:e.description}),(0,s.jsx)(c.Badge,{className:"bg-temple/10 text-temple",children:e.benefit})]})]},a))})]})}),(0,s.jsx)("section",{className:"py-20",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4",children:[(0,s.jsxs)("div",{className:"text-center mb-16",children:[(0,s.jsx)("h2",{className:"text-4xl font-bold text-charcoal mb-6",children:"Historie Transformacji"}),(0,s.jsx)("p",{className:"text-xl text-wood max-w-3xl mx-auto",children:"Prawdziwe historie uczestnik\xf3w, kt\xf3rzy doświadczyli głębokiej przemiany podczas duchowych podr\xf3ży."})]}),(0,s.jsx)("div",{className:"grid lg:grid-cols-3 gap-8",children:[{name:"Magdalena Kowalska",beforeAfter:{before:"Wypalona zawodowo, w stałym stresie",after:"Odnalazłam spok\xf3j i życiową pasję"},quote:"Ta podr\xf3ż totalnie zmieniła moje życie. Wr\xf3ciłam jako nowa osoba, pełna energii i radości.",transformation:"Emotional healing"},{name:"Tomasz Nowak",beforeAfter:{before:"Zagubiony, brak kierunku w życiu",after:"Jasne cele i głęboka motywacja"},quote:"Odkryłem kim naprawdę jestem. Azja nauczyła mnie być obecnym w chwili.",transformation:"Spiritual awakening"},{name:"Karolina Wiśniewska",beforeAfter:{before:"Problemy ze snem, lęki",after:"Spok\xf3j umysłu, pewność siebie"},quote:"Medytacja i joga w tak magicznych miejscach to była terapia na najwyższym poziomie.",transformation:"Mental clarity"}].map((e,a)=>(0,s.jsx)(l.Zp,{className:"bg-white/80 backdrop-blur-sm border-temple/20",children:(0,s.jsxs)(l.Wu,{className:"p-8",children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-charcoal mb-2",children:e.name}),(0,s.jsx)(c.Badge,{className:"bg-temple/10 text-temple text-xs",children:e.transformation})]}),(0,s.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,s.jsx)("div",{className:"p-4 bg-red-50 rounded-lg",children:(0,s.jsxs)("p",{className:"text-sm text-red-700",children:[(0,s.jsx)("strong",{children:"Przed:"})," ",e.beforeAfter.before]})}),(0,s.jsx)("div",{className:"p-4 bg-green-50 rounded-lg",children:(0,s.jsxs)("p",{className:"text-sm text-green-700",children:[(0,s.jsx)("strong",{children:"Po:"})," ",e.beforeAfter.after]})})]}),(0,s.jsxs)("p",{className:"text-wood italic",children:['"',e.quote,'"']}),(0,s.jsx)("div",{className:"flex items-center gap-1 mt-4",children:[void 0,void 0,void 0,void 0,void 0].map((e,a)=>(0,s.jsx)(g.A,{className:"w-4 h-4 text-golden fill-golden"},a))})]})},a))})]})}),(0,s.jsx)("section",{className:"py-20 bg-sanctuary",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4",children:[(0,s.jsxs)("div",{className:"text-center mb-16",children:[(0,s.jsx)("h2",{className:"text-4xl font-bold text-charcoal mb-6",children:"Inwestycja w Transformację"}),(0,s.jsx)("p",{className:"text-xl text-wood max-w-3xl mx-auto",children:"Duchowe podr\xf3że to inwestycja w siebie, kt\xf3ra procentuje przez całe życie."})]}),(0,s.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12",children:[(0,s.jsx)(l.Zp,{className:"bg-white/80 backdrop-blur-sm border-temple/20",children:(0,s.jsxs)(l.Wu,{className:"p-8",children:[(0,s.jsxs)("div",{className:"text-center mb-6",children:[(0,s.jsx)("h3",{className:"text-2xl font-bold text-charcoal mb-2",children:"Bali Transformation"}),(0,s.jsx)("p",{className:"text-wood",children:"10 dni duchowej podr\xf3ży"})]}),(0,s.jsxs)("div",{className:"space-y-4 mb-8",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(y.A,{className:"w-5 h-5 text-temple"}),(0,s.jsx)("span",{className:"text-wood",children:"Daily meditation & yoga"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(y.A,{className:"w-5 h-5 text-temple"}),(0,s.jsx)("span",{className:"text-wood",children:"Świątynie i sacred sites"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(y.A,{className:"w-5 h-5 text-temple"}),(0,s.jsx)("span",{className:"text-wood",children:"Ayurveda treatments"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(y.A,{className:"w-5 h-5 text-temple"}),(0,s.jsx)("span",{className:"text-wood",children:"Spiritual guidance"})]})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-3xl font-bold text-temple mb-2",children:"3400 PLN"}),(0,s.jsx)(o.Button,{className:"w-full bg-temple hover:bg-temple/90",asChild:!0,children:(0,s.jsx)(i(),{href:"/rezerwacja?program=bali-transformation",children:"Rozpocznij Transformację"})})]})]})}),(0,s.jsx)(l.Zp,{className:"bg-white/80 backdrop-blur-sm border-temple/20",children:(0,s.jsxs)(l.Wu,{className:"p-8",children:[(0,s.jsxs)("div",{className:"text-center mb-6",children:[(0,s.jsx)("h3",{className:"text-2xl font-bold text-charcoal mb-2",children:"Sri Lanka Awakening"}),(0,s.jsx)("p",{className:"text-wood",children:"12 dni duchowego przebudzenia"})]}),(0,s.jsxs)("div",{className:"space-y-4 mb-8",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(y.A,{className:"w-5 h-5 text-temple"}),(0,s.jsx)("span",{className:"text-wood",children:"Sigiriya sunrise meditation"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(y.A,{className:"w-5 h-5 text-temple"}),(0,s.jsx)("span",{className:"text-wood",children:"Buddhist temples"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(y.A,{className:"w-5 h-5 text-temple"}),(0,s.jsx)("span",{className:"text-wood",children:"Authentic Ayurveda"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(y.A,{className:"w-5 h-5 text-temple"}),(0,s.jsx)("span",{className:"text-wood",children:"Mountain meditation"})]})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-3xl font-bold text-temple mb-2",children:"3800 PLN"}),(0,s.jsx)(o.Button,{className:"w-full bg-temple hover:bg-temple/90",asChild:!0,children:(0,s.jsx)(i(),{href:"/rezerwacja?program=srilanka-awakening",children:"Rozpocznij Przebudzenie"})})]})]})})]})]})}),(0,s.jsx)("section",{className:"py-20 bg-gradient-to-r from-temple/20 to-golden/20",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto px-4 text-center",children:[(0,s.jsx)("h2",{className:"text-4xl font-bold text-charcoal mb-6",children:"Twoja Transformacja Zaczyna Się Teraz"}),(0,s.jsx)("p",{className:"text-xl text-wood mb-12 max-w-2xl mx-auto",children:"Nie czekaj na idealny moment. Ideal moment to teraz. Dołącz do nas na transformacyjnej podr\xf3ży do Azji."}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-6 justify-center mb-12",children:[(0,s.jsx)(o.Button,{size:"lg",className:"bg-temple hover:bg-temple/90 text-xl px-12 py-8",asChild:!0,children:(0,s.jsxs)(i(),{href:"/rezerwacja",children:["Zarezerwuj Transformację",(0,s.jsx)(p,{className:"w-6 h-6 ml-3"})]})}),(0,s.jsx)(o.Button,{size:"lg",variant:"outline",className:"border-temple text-temple hover:bg-temple/10 text-xl px-12 py-8",asChild:!0,children:(0,s.jsx)(i(),{href:"/kontakt",children:"Porozmawiajmy"})})]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row items-center justify-center gap-8 text-wood",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(w.A,{className:"w-5 h-5"}),(0,s.jsx)("span",{children:"+48 666 777 888"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(v.A,{className:"w-5 h-5"}),(0,s.jsx)("span",{children:"<EMAIL>"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(N.A,{className:"w-5 h-5"}),(0,s.jsx)("span",{children:"@fly_with_bakasana"})]})]})]})}),(0,s.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify({"@context":"https://schema.org","@type":"Service",name:"Transformacyjne Podr\xf3że Azja",description:"Duchowe retreaty jogi na Bali i Sri Lanka prowadzące do głębokiej transformacji osobistej",provider:{"@type":"Organization",name:"BAKASANA",url:"https://bakasana-travel.blog"},areaServed:{"@type":"Country",name:"Poland"},audience:{"@type":"Audience",audienceType:"people seeking spiritual transformation"},offers:{"@type":"Offer",price:"3400",priceCurrency:"PLN",availability:"https://schema.org/InStock"}})}})]})}}};var a=require("../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),s=a.X(0,[4447,4693,6533,3384,8947,2697],()=>t(17117));module.exports=s})();