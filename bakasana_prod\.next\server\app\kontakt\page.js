(()=>{var e={};e.id=9937,e.ids=[9937],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13474:(e,a,t)=>{Promise.resolve().then(t.bind(t,43022)),Promise.resolve().then(t.bind(t,64022))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34109:(e,a,t)=>{"use strict";t.d(a,{A:()=>o});var s=t(37413);let r=(0,t(61120).memo)(()=>(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("link",{rel:"preconnect",href:"//www.google-analytics.com"}),(0,s.jsx)("link",{rel:"preconnect",href:"//www.googletagmanager.com"}),(0,s.jsx)("link",{rel:"preconnect",href:"//connect.facebook.net"}),(0,s.jsx)("link",{rel:"preconnect",href:"//api.web3forms.com"})]}));r.displayName="FormPreconnect";let o=r},40781:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>p,tree:()=>c});var s=t(65239),r=t(48088),o=t(88170),n=t.n(o),i=t(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(a,l);let c={children:["",{children:["kontakt",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,65513)),"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\kontakt\\page.jsx"]}]},{metadata:{icon:[],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,47103))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,39769)),"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\layout.jsx"],error:[()=>Promise.resolve().then(t.bind(t,42201)),"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\error.jsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,43867)),"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\not-found.jsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,47103))).default(e)],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\kontakt\\page.jsx"],m={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/kontakt/page",pathname:"/kontakt",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},43022:(e,a,t)=>{"use strict";t.d(a,{default:()=>o});var s=t(60687),r=t(43210);function o(){let[e,a]=(0,r.useState)({name:"",email:"",phone:"",message:"",retreatInterest:"",honeypot:""}),[t,o]=(0,r.useState)(""),[n,i]=(0,r.useState)(!1),[l,c]=(0,r.useState)({}),[d,m]=(0,r.useState)({}),p=(e,a)=>{switch(e){case"name":return a.length<2?"Imię musi mieć co najmniej 2 znaki":"";case"email":return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(a)?"":"Podaj prawidłowy adres email";case"phone":return a&&!/^[\+]?[0-9\s\-\(\)]{9,}$/.test(a)?"Podaj prawidłowy numer telefonu":"";case"message":return a.length<10?"Wiadomość musi mieć co najmniej 10 znak\xf3w":"";default:return""}},h=t=>{let{id:s,value:r}=t.target;if(a({...e,[s]:r}),d[s]){let e=p(s,r);c({...l,[s]:e})}},x=e=>{let{id:a,value:t}=e.target;m({...d,[a]:!0});let s=p(a,t);c({...l,[a]:s})},b=async t=>{if(t.preventDefault(),e.honeypot)return;let s={};if(Object.keys(e).forEach(a=>{if("honeypot"!==a&&"phone"!==a&&"retreatInterest"!==a){let t=p(a,e[a]);t&&(s[a]=t)}}),Object.keys(s).length>0){c(s),m(Object.keys(e).reduce((e,a)=>({...e,[a]:!0}),{})),o("Proszę poprawić błędy w formularzu");return}i(!0),o("Wysyłanie...");try{let t=await fetch("https://api.web3forms.com/submit",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify({access_key:"your-web3forms-access-key",name:e.name,email:e.email,phone:e.phone,message:e.message,retreat_interest:e.retreatInterest,subject:`Nowa wiadomość z BAKASANA od ${e.name}`,from_name:"BAKASANA",to_email:"<EMAIL>",source:"contact_form",timestamp:new Date().toISOString(),user_agent:navigator.userAgent})}),s=await t.json();if(s.success)o("✅ Wiadomość wysłana pomyślnie! Odpowiemy w ciągu 24 godzin."),a({name:"",email:"",phone:"",message:"",retreatInterest:"",honeypot:""}),c({}),m({});else throw Error(s.message||"Błąd wysyłania")}catch(e){o("Wystąpił błąd. Spr\xf3buj ponownie lub napisz bezpoś<NAME_EMAIL>")}finally{i(!1),setTimeout(()=>o(""),8e3)}};return(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-20 items-start max-w-5xl mx-auto",children:[(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{className:"text-center lg:text-left",children:[(0,s.jsx)("h2",{className:"section-header mb-6",children:"Napisz do nas"}),(0,s.jsx)("p",{className:"body-text opacity-80",children:"Każda wiadomość jest dla nas ważna"})]}),(0,s.jsxs)("form",{onSubmit:b,className:"space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"name",className:"block subtle-text mb-3",children:"Imię *"}),(0,s.jsx)("input",{type:"text",id:"name",value:e.name,onChange:h,onBlur:x,required:!0,className:`w-full px-0 py-4 bg-transparent border-0 border-b transition-colors text-charcoal placeholder-stone/50 ${l.name?"border-red-500":"border-stone/30 focus:border-temple-gold"} focus:outline-none`,placeholder:"Twoje imię","aria-describedby":l.name?"name-error":void 0}),l.name&&(0,s.jsx)("p",{id:"name-error",className:"text-red-500 text-sm mt-2",children:l.name})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block subtle-text mb-3",children:"Email *"}),(0,s.jsx)("input",{type:"email",id:"email",value:e.email,onChange:h,onBlur:x,required:!0,className:`w-full px-0 py-4 bg-transparent border-0 border-b transition-colors text-charcoal placeholder-stone/50 ${l.email?"border-red-500":"border-stone/30 focus:border-temple-gold"} focus:outline-none`,placeholder:"<EMAIL>","aria-describedby":l.email?"email-error":void 0}),l.email&&(0,s.jsx)("p",{id:"email-error",className:"text-red-500 text-sm mt-2",children:l.email})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"phone",className:"block subtle-text mb-3",children:"Telefon (opcjonalnie)"}),(0,s.jsx)("input",{type:"tel",id:"phone",value:e.phone,onChange:h,onBlur:x,className:`w-full px-0 py-4 bg-transparent border-0 border-b transition-colors text-charcoal placeholder-stone/50 ${l.phone?"border-red-500":"border-stone/30 focus:border-temple-gold"} focus:outline-none`,placeholder:"+48 123 456 789","aria-describedby":l.phone?"phone-error":void 0}),l.phone&&(0,s.jsx)("p",{id:"phone-error",className:"text-red-500 text-sm mt-2",children:l.phone})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"retreatInterest",className:"block subtle-text mb-3",children:"Interesuje Cię (opcjonalnie)"}),(0,s.jsxs)("select",{id:"retreatInterest",value:e.retreatInterest,onChange:h,className:"w-full px-0 py-4 bg-transparent border-0 border-b border-stone/30 focus:border-temple-gold focus:outline-none transition-colors text-charcoal",children:[(0,s.jsx)("option",{value:"",children:"Wybierz opcję"}),(0,s.jsx)("option",{value:"retreat-bali",children:"Retreat na Bali"}),(0,s.jsx)("option",{value:"retreat-poland",children:"Retreat w Polsce"}),(0,s.jsx)("option",{value:"private-sessions",children:"Sesje indywidualne"}),(0,s.jsx)("option",{value:"online-classes",children:"Zajęcia online"}),(0,s.jsx)("option",{value:"workshops",children:"Warsztaty"}),(0,s.jsx)("option",{value:"other",children:"Inne"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"message",className:"block subtle-text mb-3",children:"Wiadomość *"}),(0,s.jsx)("textarea",{id:"message",value:e.message,onChange:h,onBlur:x,required:!0,rows:6,className:`w-full px-0 py-4 bg-transparent border-0 border-b transition-colors text-charcoal placeholder-stone/50 resize-none ${l.message?"border-red-500":"border-stone/30 focus:border-temple-gold"} focus:outline-none`,placeholder:"Podziel się swoimi myślami...","aria-describedby":l.message?"message-error":void 0}),l.message&&(0,s.jsx)("p",{id:"message-error",className:"text-red-500 text-sm mt-2",children:l.message})]}),(0,s.jsx)("input",{type:"text",id:"honeypot",name:"honeypot",value:e.honeypot,onChange:h,style:{display:"none"},tabIndex:"-1",autoComplete:"off"}),(0,s.jsxs)("div",{className:"pt-8",children:[(0,s.jsx)("button",{type:"submit",disabled:n,className:`btn-ghost btn-primary ${n?"opacity-50 cursor-not-allowed":""}`,children:n?"Wysyłanie...":"Wyślij Wiadomość"}),t&&(0,s.jsx)("p",{className:"text-sm text-charcoal/70 font-light mt-4 max-w-xs",children:t})]})]})]}),(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{className:"text-center lg:text-left",children:[(0,s.jsx)("h3",{className:"section-header mb-6",children:"Znajdź nas"}),(0,s.jsx)("p",{className:"body-text opacity-80 mb-8",children:"Połączmy się w przestrzeni cyfrowej"})]}),(0,s.jsx)("div",{className:"space-y-6",children:[{href:"https://www.instagram.com/fly_with_bakasana?igsh=MWpmanNpeHVodTlubA%3D%3D&utm_source=qr",label:"Instagram",aria:"Profil na Instagramie"},{href:"https://www.facebook.com/p/Fly-with-bakasana-100077568306563/",label:"Facebook",aria:"Profil na Facebooku"},{href:"mailto:<EMAIL>",label:"Email",aria:"Kontakt email"}].map(e=>(0,s.jsxs)("a",{href:e.href,target:"_blank",rel:"noopener noreferrer","aria-label":e.aria,className:"block p-6 hover:opacity-70 transition-opacity duration-200 text-center lg:text-left",children:[(0,s.jsx)("h4",{className:"font-light text-charcoal mb-2 tracking-wide text-lg",children:e.label}),(0,s.jsxs)("p",{className:"text-sm text-stone font-light",children:["Instagram"===e.label&&"Codzienne inspiracje","Facebook"===e.label&&"Społeczność BAKASANA","Email"===e.label&&"Bezpośredni kontakt"]})]},e.label))}),(0,s.jsx)("div",{className:"flex items-center justify-center lg:justify-start my-12",children:(0,s.jsxs)("div",{className:"flex items-center gap-4 text-temple-gold/60",children:[(0,s.jsx)("div",{className:"w-12 h-px bg-temple-gold/30"}),(0,s.jsx)("span",{className:"text-lg opacity-60",children:"ॐ"}),(0,s.jsx)("div",{className:"w-12 h-px bg-temple-gold/30"})]})}),(0,s.jsxs)("div",{className:"text-center lg:text-left",children:[(0,s.jsx)("p",{className:"text-sm text-stone font-light italic tracking-wide",children:'"Każda podr\xf3ż zaczyna się od jednego kroku..."'}),(0,s.jsx)("p",{className:"text-xs text-temple-gold font-light tracking-wide uppercase mt-2",children:"Om Swastiastu"})]})]})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65513:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>l});var s=t(37413),r=t(61120),o=t(68104),n=t(84220),i=t(34109);function l(){return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.A,{}),(0,s.jsxs)("div",{className:"bg-sanctuary min-h-screen",children:[(0,s.jsx)("section",{className:"magazine-hero",children:(0,s.jsxs)("div",{className:"magazine-hero-content",children:[(0,s.jsx)("div",{className:"magazine-header-line"}),(0,s.jsx)("h1",{className:"magazine-title",children:"Kontakt"}),(0,s.jsx)("p",{className:"magazine-subtitle",children:"Zacznij swoją podr\xf3ż do siebie"}),(0,s.jsx)("div",{className:"magazine-meta",children:"Bali • Sri Lanka • Duchowa Transformacja"}),(0,s.jsx)("div",{className:"magazine-header-line"})]})}),(0,s.jsxs)("section",{className:"container",children:[(0,s.jsxs)("div",{className:"text-center mb-20",children:[(0,s.jsx)("div",{className:"section-divider mb-12"}),(0,s.jsx)("h2",{className:"section-header mb-8",children:"Porozmawiajmy o Twojej podr\xf3ży"}),(0,s.jsx)("p",{className:"body-text max-w-3xl mx-auto mb-8 opacity-80",children:"Każda transformacja zaczyna się od pierwszego kroku. Napisz do mnie, a wsp\xf3lnie znajdziemy idealny retreat, kt\xf3ry otworzy przed Tobą drzwi do wewnętrznego spokoju i odkrycia prawdziwej siebie."}),(0,s.jsx)("div",{className:"flex items-center justify-center my-12",children:(0,s.jsxs)("div",{className:"flex items-center gap-4 text-temple-gold/60",children:[(0,s.jsx)("div",{className:"w-12 h-px bg-temple-gold/30"}),(0,s.jsx)("span",{className:"text-xl opacity-60",children:"ॐ"}),(0,s.jsx)("div",{className:"w-12 h-px bg-temple-gold/30"})]})}),(0,s.jsx)("p",{className:"body-text italic text-temple-gold/80 mb-8",children:'"Najdłuższa podr\xf3ż zaczyna się od jednego kroku"'})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-12 mb-20 max-w-4xl mx-auto",children:[(0,s.jsxs)("div",{className:"text-center p-8",children:[(0,s.jsx)("h3",{className:"font-light text-charcoal mb-4 tracking-wide",children:"WhatsApp"}),(0,s.jsx)("p",{className:"text-sm text-stone font-light mb-4",children:"Szybki kontakt"}),(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsx)(n.default,{size:"md",variant:"button",className:"px-6 py-3 text-center rounded-lg"})})]}),(0,s.jsxs)("div",{className:"text-center p-8",children:[(0,s.jsx)("h3",{className:"font-light text-charcoal mb-4 tracking-wide",children:"Instagram"}),(0,s.jsx)("p",{className:"text-sm text-stone font-light mb-4",children:"Codzienne inspiracje"}),(0,s.jsx)("a",{href:"https://www.instagram.com/fly_with_bakasana",target:"_blank",rel:"noopener noreferrer",className:"btn-ghost",children:"Obserwuj"})]}),(0,s.jsxs)("div",{className:"text-center p-8",children:[(0,s.jsx)("h3",{className:"font-light text-charcoal mb-4 tracking-wide",children:"Email"}),(0,s.jsx)("p",{className:"text-sm text-stone font-light mb-4",children:"Szczeg\xf3łowe zapytania"}),(0,s.jsx)("a",{href:"mailto:<EMAIL>",className:"btn-ghost",children:"Napisz email"})]})]}),(0,s.jsxs)("div",{id:"contact-form",className:"max-w-4xl mx-auto",children:[(0,s.jsxs)("div",{className:"text-center mb-16",children:[(0,s.jsx)("h3",{className:"section-header mb-8",children:"Formularz kontaktowy"}),(0,s.jsx)("p",{className:"body-text opacity-80",children:"Wypełnij formularz, a odpowiem w ciągu 24 godzin"})]}),(0,s.jsx)(r.Suspense,{fallback:(0,s.jsx)("div",{className:"flex items-center justify-center h-[40vh] text-stone",children:"Ładowanie..."}),children:(0,s.jsx)(o.default,{})})]})]}),(0,s.jsx)("div",{className:"fixed bottom-6 right-6 z-50",children:(0,s.jsx)(n.default,{size:"lg",message:"Cześć Julia! Chciałabym/chciałbym porozmawiać o retreatach. Czy możemy się skontaktować?"})})]})]})}},68104:(e,a,t)=>{"use strict";t.d(a,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\kontakt\\ContactForm.jsx","default")},73306:(e,a,t)=>{Promise.resolve().then(t.bind(t,68104)),Promise.resolve().then(t.bind(t,84220))},79551:e=>{"use strict";e.exports=require("url")},84220:(e,a,t)=>{"use strict";t.d(a,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\PerformantWhatsApp.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\PerformantWhatsApp.jsx","default")}};var a=require("../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),s=a.X(0,[4447,4693,2697],()=>t(40781));module.exports=s})();