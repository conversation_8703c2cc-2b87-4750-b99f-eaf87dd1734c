(()=>{var e={};e.id=7302,e.ids=[7302],e.modules={250:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>o});var i=a(60687),n=a(30474),r=a(72169);function o(){let{isDesktop:e,isTablet:t,isMobile:a}=(0,r.wf)();(0,r.XD)(e,t,a);let o=(0,r.aU)(e,t,a),s=[{name:"Konsultacja",duration:"30 min",note:"Poznanie i ustalenie cel\xf3w"},{name:"<PERSON><PERSON><PERSON> indywidualna",duration:"60 min",note:"Pełna praktyka dostosowana do Ciebie"},{name:"Cykl 4 sesji",duration:"60 min",note:"Pogłębienie i rozw\xf3j praktyki"},{name:"Cykl 8 sesji",duration:"60 min",note:"Transformacja i stabilizacja"}],l=[{name:"Małe grupy",size:"4-6 os\xf3b",note:"Intymna atmosfera"},{name:"Średnie grupy",size:"7-12 os\xf3b",note:"Energia wsp\xf3lnoty"},{name:"Miesięczny cykl",size:"8 praktyk",note:"Regularna praktyka"},{name:"Kwartalny cykl",size:"24 praktyki",note:"Głęboka transformacja"}];return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify({"@context":"https://schema.org","@type":"Service",name:"Zajęcia Jogi Online - Julia Jakubowicz",description:"Prywatne lekcje jogi online i grupowe zajęcia online z certyfikowaną instruktorką jogi RYT 500.",provider:{"@type":"Person",name:"Julia Jakubowicz"}})}}),(0,i.jsxs)("main",{className:"bg-sanctuary min-h-screen",children:[(0,i.jsx)("section",{className:"magazine-hero",children:(0,i.jsxs)("div",{className:"magazine-hero-content",children:[(0,i.jsx)("div",{className:"magazine-header-line"}),(0,i.jsx)("h1",{className:"magazine-title",children:"Online"}),(0,i.jsx)("p",{className:"magazine-subtitle",children:"Praktyka jogi w Twojej przestrzeni"}),(0,i.jsx)("div",{className:"magazine-meta",children:"Sesje indywidualne i grupowe"}),(0,i.jsx)("div",{className:"magazine-header-line"})]})}),(0,i.jsx)("section",{style:o.section,children:(0,i.jsxs)("div",{style:o.grid,children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{style:o.sectionTitle,children:"Praktyka indywidualna"}),(0,i.jsx)("p",{style:{fontSize:"17px",fontFamily:"Inter",fontWeight:300,color:"#5B5754",lineHeight:"1.8",marginBottom:"32px"},children:"Osobista przestrzeń do odkrywania głębi praktyki jogi w zaciszu własnego domu."}),(0,i.jsx)("div",{style:{fontSize:"15px",fontFamily:"Inter",fontWeight:300,color:"#3D3A37",lineHeight:"1.9",letterSpacing:"0.02em",marginBottom:"56px",textAlign:"left"},children:"Każda sesja jest starannie dostosowana do Twojego poziomu zaawansowania, obecnych potrzeb ciała i ducha. Wsp\xf3lnie tworzymy przestrzeń, w kt\xf3rej możesz bezpiecznie eksplorować r\xf3żne aspekty jogi - od asany po pranayamę i medytację."}),(0,i.jsxs)("div",{style:{marginBottom:"64px"},children:[(0,i.jsx)("h3",{style:{fontSize:"20px",fontFamily:"Cormorant Garamond",fontWeight:400,color:"#3D3A37",marginBottom:"28px"},children:"Struktura sesji"}),(0,i.jsx)("div",{children:["Wstępna rozmowa o intencjach i celach","Program stworzony specjalnie dla Ciebie","Delikatne korekty w czasie rzeczywistym","Nagranie sesji do kontynuacji praktyki","Wsparcie między sesjami"].map((e,t)=>(0,i.jsx)("div",{style:{padding:"16px 0",paddingLeft:"24px",borderLeft:"1px solid rgba(196, 165, 117, 0.1)",position:"relative"},children:(0,i.jsx)("span",{style:{fontSize:"14px",fontFamily:"Inter",fontWeight:300,color:"#4A4744"},children:e})},t))})]}),(0,i.jsxs)("div",{style:{marginTop:"64px"},children:[(0,i.jsx)("h3",{style:{fontSize:"20px",fontFamily:"Cormorant Garamond",fontWeight:400,color:"#3D3A37",marginBottom:"28px"},children:"Formy spotkań"}),(0,i.jsx)("div",{children:s.map((e,t)=>(0,i.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"20px 0",borderBottom:t===s.length-1?"none":"1px solid rgba(139, 133, 127, 0.15)"},children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{style:{fontSize:"15px",fontFamily:"Inter",fontWeight:400,color:"#3D3A37",margin:"0 0 4px 0"},children:e.name}),(0,i.jsx)("p",{style:{fontSize:"13px",fontFamily:"Inter",fontWeight:300,color:"#8B857F",margin:0},children:e.duration})]}),(0,i.jsx)("span",{style:{fontSize:"13px",fontFamily:"Inter",fontWeight:300,color:"#8B857F"},children:e.note})]},t))})]})]}),(0,i.jsx)("div",{style:o.imageContainer,children:(0,i.jsx)(n.default,{src:"/images/profile/omnie-opt.webp",alt:"Indywidualne sesje jogi online",fill:!0,style:{objectFit:"cover",filter:"grayscale(100%) brightness(1.05)"},sizes:a?"100vw":"45vw",quality:95})})]})}),(0,i.jsx)("div",{style:o.divider}),(0,i.jsx)("section",{style:{maxWidth:"1200px",margin:"0 auto",padding:a?"0 40px":t?"0 60px":"0 80px"},children:(0,i.jsxs)("div",{style:o.gridReverse,children:[(0,i.jsx)("div",{style:o.visualElement,children:(0,i.jsxs)("div",{style:{display:"flex",alignItems:"center",justifyContent:"center",gap:a?"10px":"20px"},children:[(0,i.jsx)("div",{style:{width:a?"40px":"60px",height:a?"40px":"60px",border:"1px solid rgba(196, 165, 117, 0.15)",borderRadius:"50%",transform:a?"translateX(5px)":"translateX(10px)"}}),(0,i.jsx)("div",{style:{width:a?"40px":"60px",height:a?"40px":"60px",border:"1px solid rgba(196, 165, 117, 0.15)",borderRadius:"50%",transform:a?"translateX(-5px)":"translateX(-10px)"}}),(0,i.jsx)("div",{style:{width:a?"40px":"60px",height:a?"40px":"60px",border:"1px solid rgba(196, 165, 117, 0.15)",borderRadius:"50%",transform:a?"translateX(-15px)":"translateX(-30px)"}})]})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{style:o.sectionTitle,children:"Praktyka grupowa"}),(0,i.jsx)("p",{style:{fontSize:"17px",fontFamily:"Inter",fontWeight:300,color:"#5B5754",lineHeight:"1.8",marginBottom:"32px"},children:"Energia wsp\xf3lnoty połączona z indywidualną uwagą w małych, intymnych grupach."}),(0,i.jsx)("div",{style:{fontSize:"15px",fontFamily:"Inter",fontWeight:300,color:"#3D3A37",lineHeight:"1.9",letterSpacing:"0.02em",marginBottom:"56px",textAlign:"left"},children:"Praktykuj w towarzystwie innych, czerpiąc siłę z energii grupy, jednocześnie otrzymując personalną uwagę. Każda osoba jest widziana i wspierana w swojej unikalnej podr\xf3ży."}),(0,i.jsxs)("div",{style:{marginBottom:"64px"},children:[(0,i.jsx)("h3",{style:{fontSize:"20px",fontFamily:"Cormorant Garamond",fontWeight:400,color:"#3D3A37",marginBottom:"28px"},children:"Harmonogram zajęć"}),(0,i.jsx)("div",{children:[{day:"Poniedziałek",time:"18:00",type:"Hatha Yoga",level:"dla początkujących"},{day:"Środa",time:"19:00",type:"Vinyasa Flow",level:"dla praktykujących"},{day:"Piątek",time:"17:30",type:"Yin Yoga",level:"dla wszystkich"},{day:"Sobota",time:"10:00",type:"Yoga Terapeutyczna",level:"dla ciała i duszy"}].map((e,t)=>(0,i.jsxs)("div",{style:{padding:"18px 0",fontSize:"14px",fontFamily:"Inter",color:"#3D3A37"},children:[(0,i.jsx)("span",{style:{fontWeight:400},children:e.day}),(0,i.jsx)("span",{style:{color:"#D4D0CC",margin:"0 8px"},children:"|"}),(0,i.jsx)("span",{style:{fontWeight:300},children:e.time}),(0,i.jsx)("span",{style:{color:"#D4D0CC",margin:"0 8px"},children:"|"}),(0,i.jsx)("span",{style:{fontWeight:300,color:"#6B6560"},children:e.type})]},t))})]}),(0,i.jsxs)("div",{style:{marginTop:"64px"},children:[(0,i.jsx)("h3",{style:{fontSize:"20px",fontFamily:"Cormorant Garamond",fontWeight:400,color:"#3D3A37",marginBottom:"28px"},children:"Formy spotkań"}),(0,i.jsx)("div",{children:l.map((e,t)=>(0,i.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"20px 0",borderBottom:t===l.length-1?"none":"1px solid rgba(139, 133, 127, 0.15)"},children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{style:{fontSize:"15px",fontFamily:"Inter",fontWeight:400,color:"#3D3A37",margin:"0 0 4px 0"},children:e.name}),(0,i.jsx)("p",{style:{fontSize:"13px",fontFamily:"Inter",fontWeight:300,color:"#8B857F",margin:0},children:e.size})]}),(0,i.jsx)("span",{style:{fontSize:"13px",fontFamily:"Inter",fontWeight:300,color:"#8B857F"},children:e.note})]},t))})]})]})]})}),(0,i.jsxs)("section",{style:o.ctaSection,children:[(0,i.jsx)("h2",{style:o.ctaTitle,children:"Rozpocznij swoją praktykę"}),(0,i.jsx)("p",{style:{fontSize:"16px",fontFamily:"Inter",fontWeight:300,color:"#5B5754",lineHeight:"1.7",marginBottom:"48px"},children:"Skontaktuj się ze mną, aby om\xf3wić szczeg\xf3ły i znaleźć formę praktyki, kt\xf3ra będzie odpowiadać Twoim potrzebom."}),(0,i.jsxs)("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",gap:"16px"},children:[(0,i.jsx)("button",{style:{padding:"16px 48px",border:"1px solid rgba(61, 58, 55, 0.3)",backgroundColor:"transparent",fontSize:"12px",fontFamily:"Inter",fontWeight:400,letterSpacing:"0.15em",textTransform:"uppercase",color:"#3D3A37",cursor:"pointer",transition:"all 0.3s ease"},onMouseOver:e=>{e.target.style.backgroundColor="#3D3A37",e.target.style.color="#FDFAF7"},onMouseOut:e=>{e.target.style.backgroundColor="transparent",e.target.style.color="#3D3A37"},onClick:()=>window.location.href="/kontakt",children:"UM\xd3W SESJĘ"}),(0,i.jsx)("p",{style:{fontSize:"13px",fontFamily:"Inter",fontWeight:300,color:"#8B857F",margin:0},children:"Pierwsza konsultacja 30 min bez opłat"})]})]})]})]})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30474:(e,t,a)=>{"use strict";a.d(t,{default:()=>n.a});var i=a(31261),n=a.n(i)},31261:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{default:function(){return l},getImageProps:function(){return s}});let i=a(59630),n=a(44953),r=a(46533),o=i._(a(1933));function s(e){let{props:t}=(0,n.getImgProps)(e,{defaultLoader:o.default,imgConf:{deviceSizes:[480,640,768,1024,1200,1920],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,a]of Object.entries(t))void 0===a&&delete t[e];return{props:t}}let l=r.Image},33873:e=>{"use strict";e.exports=require("path")},59724:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>i});let i=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\zajecia-online\\\\page.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\zajecia-online\\page.jsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64897:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>o.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>x,tree:()=>p});var i=a(65239),n=a(48088),r=a(88170),o=a.n(r),s=a(30893),l={};for(let e in s)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>s[e]);a.d(t,l);let p={children:["",{children:["zajecia-online",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,59724)),"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\zajecia-online\\page.jsx"]}]},{metadata:{icon:[],apple:[],openGraph:[async e=>(await Promise.resolve().then(a.bind(a,47103))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,39769)),"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\layout.jsx"],error:[()=>Promise.resolve().then(a.bind(a,42201)),"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\error.jsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,43867)),"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\not-found.jsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[],apple:[],openGraph:[async e=>(await Promise.resolve().then(a.bind(a,47103))).default(e)],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\zajecia-online\\page.jsx"],c={require:a,loadChunk:()=>Promise.resolve()},x=new i.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/zajecia-online/page",pathname:"/zajecia-online",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},72169:(e,t,a)=>{"use strict";a.d(t,{XD:()=>r,aU:()=>o,wf:()=>n});var i=a(43210);function n(){let[e,t]=(0,i.useState)(!0),[a,n]=(0,i.useState)(!1),[r,o]=(0,i.useState)(!1),[s,l]=(0,i.useState)(!1);return s?{isDesktop:e,isTablet:a,isMobile:r}:{isDesktop:!0,isTablet:!1,isMobile:!1}}let r=(e,t,a)=>({hero:{height:"100vh",minHeight:"600px",backgroundColor:"#FDFAF7",display:"flex",alignItems:"center",justifyContent:"center",position:"relative",backgroundImage:`
      radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.02) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(0, 0, 0, 0.01) 0%, transparent 50%)
    `},heroContent:{textAlign:"center",transform:"translateY(-30px)",padding:a?"0 20px":"0"},heroTitle:{fontSize:a?"56px":t?"72px":"110px",fontFamily:"Cormorant Garamond",fontWeight:300,color:"#3D3A37",letterSpacing:"0.08em",marginBottom:"40px",lineHeight:"1.1"},heroSubtitle:{fontSize:"15px",fontFamily:"Inter",fontWeight:300,color:"#6B6560",letterSpacing:"0.15em",opacity:.8,marginBottom:0},heroMeta:{position:"absolute",bottom:"15%",left:"50%",transform:"translateX(-50%)",textAlign:"center",fontSize:"12px",fontFamily:"Inter",fontWeight:300,color:"#8B857F",letterSpacing:"0.2em",margin:0}}),o=(e,t,a)=>({section:{marginTop:a?"60px":"100px",maxWidth:"1200px",margin:`${a?"60px":"100px"} auto 0`,padding:a?"0 40px":t?"0 60px":"0 80px"},grid:{display:"grid",gridTemplateColumns:a?"1fr":"55% 45%",gap:a?"40px":t?"60px":"100px",alignItems:"start"},gridReverse:{display:"grid",gridTemplateColumns:a?"1fr":"45% 55%",gap:a?"40px":t?"60px":"100px",alignItems:"start"},sectionTitle:{fontSize:a?"32px":t?"38px":"48px",fontFamily:"Cormorant Garamond",fontWeight:300,color:"#3D3A37",letterSpacing:"0.02em",marginBottom:"36px",lineHeight:"1.2"},imageContainer:{height:a?"300px":"580px",position:"relative",aspectRatio:"3/4"},visualElement:{height:a?"300px":"480px",backgroundColor:"rgba(250, 248, 245, 0.3)",display:"flex",alignItems:"center",justifyContent:"center",position:"relative"},ctaSection:{marginTop:a?"60px":"100px",textAlign:"center",maxWidth:"600px",margin:`${a?"60px":"100px"} auto 0`,paddingBottom:a?"60px":"100px",padding:a?"0 40px":"0"},ctaTitle:{fontSize:a?"28px":t?"34px":"42px",fontFamily:"Cormorant Garamond",fontWeight:300,color:"#3D3A37",marginBottom:"24px",lineHeight:"1.3"},divider:{width:"40px",height:"1px",backgroundColor:"#C4A575",opacity:.25,margin:`${a?"60px":"100px"} auto`}})},79551:e=>{"use strict";e.exports=require("url")},93918:(e,t,a)=>{Promise.resolve().then(a.bind(a,250))},98646:(e,t,a)=>{Promise.resolve().then(a.bind(a,59724))}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),i=t.X(0,[4447,4693,6533,2697],()=>a(64897));module.exports=i})();