(()=>{var e={};e.id=2145,e.ids=[2145],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21546:(e,a,t)=>{Promise.resolve().then(t.t.bind(t,4536,23)),Promise.resolve().then(t.t.bind(t,49603,23)),Promise.resolve().then(t.bind(t,86810)),Promise.resolve().then(t.bind(t,77691))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36440:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(26373).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},41382:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(26373).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},49046:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(26373).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},53148:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(26373).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},56074:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(26373).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},61227:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(26373).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},62820:(e,a,t)=>{"use strict";t.d(a,{Badge:()=>o});var s=t(60687);t(43210);var r=t(24224),i=t(65222);let n=(0,r.F)("inline-flex items-center border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:a,...t}){return(0,s.jsx)("div",{className:(0,i.cn)(n({variant:a}),e),...t})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66007:(e,a,t)=>{"use strict";t.d(a,{Zp:()=>c,Wu:()=>d});var s=t(37413),r=t(61120),i=t.n(r),n=t(75986),o=t(8974);function l(...e){return(0,o.QP)((0,n.$)(e))}let c=i().forwardRef(({className:e,...a},t)=>(0,s.jsx)("div",{ref:t,className:l("bg-sanctuary shadow-subtle transition-all duration-300 hover:shadow-premium-shadow",e),...a}));c.displayName="Card",i().forwardRef(({className:e,...a},t)=>(0,s.jsx)("div",{ref:t,className:l("flex flex-col space-y-1.5 p-6",e),...a})).displayName="CardHeader",i().forwardRef(({className:e,...a},t)=>(0,s.jsx)("h3",{ref:t,className:l("font-cormorant text-2xl font-light leading-none tracking-tight text-charcoal",e),...a})).displayName="CardTitle",i().forwardRef(({className:e,...a},t)=>(0,s.jsx)("p",{ref:t,className:l("text-sm text-sage leading-relaxed",e),...a})).displayName="CardDescription";let d=i().forwardRef(({className:e,...a},t)=>(0,s.jsx)("div",{ref:t,className:l("p-6 pt-0",e),...a}));d.displayName="CardContent",i().forwardRef(({className:e,...a},t)=>(0,s.jsx)("div",{ref:t,className:l("flex items-center p-6 pt-0",e),...a})).displayName="CardFooter"},67719:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(26373).A)("mountain",[["path",{d:"m8 3 4 8 5-5 5 15H2L8 3z",key:"otkl63"}]])},76717:(e,a,t)=>{"use strict";t.d(a,{Button:()=>c});var s=t(60687),r=t(43210),i=t(81391),n=t(24224),o=t(65222);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap font-light transition-opacity duration-300 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-offset-1 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{primary:"bg-transparent border border-charcoal text-charcoal hover:opacity-70 font-inter text-xs font-light letter-spacing-wide uppercase px-12 py-4",secondary:"bg-transparent border border-stone text-stone hover:opacity-70 font-inter text-xs font-light letter-spacing-wide uppercase px-12 py-4",accent:"bg-transparent border border-temple-gold text-temple-gold hover:opacity-70 font-inter text-xs font-light letter-spacing-wide uppercase px-12 py-4",ghost:"bg-transparent text-charcoal hover:opacity-70 font-inter text-xs font-light letter-spacing-wide uppercase px-12 py-4",outline:"bg-transparent border border-stone/20 text-charcoal hover:opacity-70",hero:"bg-transparent border border-stone/10 text-charcoal hover:opacity-70"},size:{sm:"px-8 py-3 text-xs",default:"px-12 py-4 text-xs",lg:"px-16 py-5 text-sm",icon:"h-9 w-9 px-0"}},defaultVariants:{variant:"primary",size:"default"}}),c=r.forwardRef(({className:e,variant:a,size:t,asChild:r=!1,...n},c)=>{let d=r?i.DX:"button";return(0,s.jsx)(d,{className:(0,o.cn)(l({variant:a,size:t,className:e})),ref:c,...n})});c.displayName="Button"},77691:(e,a,t)=>{"use strict";t.d(a,{Button:()=>r});var s=t(12907);let r=(0,s.registerClientReference)(function(){throw Error("Attempted to call Button() from the server but Button is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\button.jsx","Button");(0,s.registerClientReference)(function(){throw Error("Attempted to call buttonVariants() from the server but buttonVariants is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\button.jsx","buttonVariants")},79551:e=>{"use strict";e.exports=require("url")},81523:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>w,metadata:()=>k});var s=t(37413),r=t(4536),i=t.n(r),n=t(53384),o=t(77691),l=t(66007),c=t(86810),d=t(67719),p=t(85838),m=t(41382),x=t(61227),h=t(36440),u=t(53148),g=t(49972),j=t(49046),y=t(56074),b=t(71750),f=t(60343),v=t(56378);let k={title:"Joga Sri Lanka Retreat 2025 - Transformacyjne Podr\xf3że z Julią Jakubowicz",description:"\uD83C\uDFC6 Najlepsze retreaty jogi na Sri Lanka 2025 z certyfikowaną instruktorką Julią Jakubowicz. Sigiriya, Kandy, Ella, ayurveda, perła Oceanu Indyjskiego. 4.9/5 ⭐ Rezerwuj!",keywords:"joga sri lanka, retreat sri lanka, sigiriya yoga, kandy medytacja, ella plantacje, ayurveda sri lanka, julia jakubowicz, transformacyjne podr\xf3że",openGraph:{title:"Joga Sri Lanka Retreat 2025 - Transformacyjne Podr\xf3że | BAKASANA",description:"\uD83C\uDFC6 Najlepsze retreaty jogi na Sri Lanka z Julią Jakubowicz. Sigiriya, Kandy, Ella, ayurveda. 4.9/5 ⭐ Rezerwuj teraz!",images:["/images/og/joga-sri-lanka-retreat-2025.jpg"]},alternates:{canonical:"https://bakasana-travel.blog/joga-sri-lanka-retreat"}},w=()=>{let e=[{icon:(0,s.jsx)(d.A,{className:"w-5 h-5 text-temple"}),title:"Sigiriya Lwia Skała",description:"Sunrise yoga na 8th wonder of the world"},{icon:(0,s.jsx)(p.A,{className:"w-5 h-5 text-temple"}),title:"Ayurveda Authentic",description:"Tradycyjne terapie i masaże ayurvedyjskie"},{icon:(0,s.jsx)(m.A,{className:"w-5 h-5 text-temple"}),title:"Małe grupy",description:"Maksymalnie 10 os\xf3b - intimate experience"},{icon:(0,s.jsx)(x.A,{className:"w-5 h-5 text-temple"}),title:"Doświadczona instruktorka",description:"Julia Jakubowicz - ekspert od Sri Lanka"}];return(0,s.jsxs)("div",{className:"min-h-screen",children:[(0,s.jsxs)("section",{className:"relative h-screen flex items-center justify-center bg-gradient-to-br from-temple/10 to-golden/10",children:[(0,s.jsx)("div",{className:"absolute inset-0 z-0",children:(0,s.jsx)(n.default,{src:"/images/destinations/sigiriya-hero.webp",alt:"Joga Sri Lanka - Sigiriya sunrise",fill:!0,className:"object-cover opacity-30",priority:!0})}),(0,s.jsxs)("div",{className:"relative z-10 text-center max-w-4xl mx-auto px-4",children:[(0,s.jsx)(c.Badge,{className:"mb-4 bg-temple/20 text-temple border-temple/30",children:"Perła Oceanu Indyjskiego"}),(0,s.jsxs)("h1",{className:"text-4xl md:text-6xl font-bold text-charcoal mb-6",children:["Joga Sri Lanka ",(0,s.jsx)("br",{}),(0,s.jsx)("span",{className:"text-temple",children:"Retreat 2025"})]}),(0,s.jsx)("p",{className:"text-xl text-wood mb-8 max-w-2xl mx-auto",children:"Transformacyjne podr\xf3że z Julią Jakubowicz. Sigiriya, Kandy, Ella, autentyczna ayurveda, duchowe doświadczenia. 10 dni odkrywania siebie."}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,s.jsx)(o.Button,{size:"lg",className:"bg-temple hover:bg-temple/90 text-lg px-8 py-6",asChild:!0,children:(0,s.jsxs)(i(),{href:"/rezerwacja",children:["Zarezerwuj Sri Lanka",(0,s.jsx)(h.A,{className:"w-5 h-5 ml-2"})]})}),(0,s.jsx)(o.Button,{size:"lg",variant:"outline",className:"border-temple text-temple hover:bg-temple/10 text-lg px-8 py-6",asChild:!0,children:(0,s.jsx)(i(),{href:"/program?destination=srilanka",children:"Zobacz Program"})})]}),(0,s.jsxs)("div",{className:"mt-8 flex items-center justify-center gap-6 text-wood",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(u.A,{className:"w-5 h-5 text-temple"}),(0,s.jsx)("span",{children:"10 dni"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(m.A,{className:"w-5 h-5 text-temple"}),(0,s.jsx)("span",{children:"Max 10 os\xf3b"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(g.A,{className:"w-5 h-5 text-golden fill-golden"}),(0,s.jsx)("span",{children:"4.9/5"})]})]})]})]}),(0,s.jsx)("section",{className:"py-16 bg-sanctuary",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4",children:[(0,s.jsxs)("div",{className:"text-center mb-12",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-charcoal mb-4",children:"Dlaczego Sri Lanka to idealne miejsce na retreat jogi?"}),(0,s.jsx)("p",{className:"text-wood max-w-2xl mx-auto",children:"Odkryj magię perły Oceanu Indyjskiego. Starożytne świątynie, ayurveda, g\xf3ry, plantacje herbaty i duchowa atmosfera."})]}),(0,s.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-6",children:e.map((e,a)=>(0,s.jsx)(l.Zp,{className:"bg-white/80 backdrop-blur-sm border-temple/20",children:(0,s.jsxs)(l.Wu,{className:"p-6 text-center",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-temple/10 rounded-full flex items-center justify-center mx-auto mb-4",children:e.icon}),(0,s.jsx)("h3",{className:"font-semibold text-charcoal mb-2",children:e.title}),(0,s.jsx)("p",{className:"text-sm text-wood",children:e.description})]})},a))})]})}),(0,s.jsx)("section",{className:"py-16",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4",children:[(0,s.jsxs)("div",{className:"text-center mb-12",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-charcoal mb-4",children:"10-dniowa podr\xf3ż przez Sri Lanka"}),(0,s.jsx)("p",{className:"text-wood max-w-2xl mx-auto",children:"Szczeg\xf3łowy program retreatu jogi obejmujący najpiękniejsze i najbardziej duchowe miejsca Sri Lanka."})]}),(0,s.jsx)("div",{className:"space-y-6",children:[{day:"Dzień 1-2",location:"Colombo → Sigiriya",activities:"Przyłot, aklimatyzacja, evening yoga"},{day:"Dzień 3-4",location:"Sigiriya - Lwia Skała",activities:"Sunrise yoga, wspinaczka, ancient ruins"},{day:"Dzień 5-6",location:"Kandy - G\xf3ry",activities:"Mountain yoga, Temple of Tooth, meditation"},{day:"Dzień 7-8",location:"Ella - Plantacje",activities:"Tea plantation yoga, Nine Arch Bridge"},{day:"Dzień 9-10",location:"Południowe plaże",activities:"Beach yoga, ayurveda treatments, relax"}].map((e,a)=>(0,s.jsx)(l.Zp,{className:"bg-white/80 backdrop-blur-sm border-temple/20",children:(0,s.jsx)(l.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center gap-6",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-temple/10 rounded-full flex items-center justify-center flex-shrink-0",children:(0,s.jsx)("span",{className:"text-temple font-bold",children:a+1})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4 mb-2",children:[(0,s.jsx)(c.Badge,{variant:"secondary",className:"bg-temple/10 text-temple",children:e.day}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(j.A,{className:"w-4 h-4 text-temple"}),(0,s.jsx)("span",{className:"font-semibold text-charcoal",children:e.location})]})]}),(0,s.jsx)("p",{className:"text-wood",children:e.activities})]})]})})},a))})]})}),(0,s.jsx)("section",{className:"py-16 bg-sanctuary",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4",children:(0,s.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12 items-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-charcoal mb-6",children:"Co zawiera retreat Sri Lanka?"}),(0,s.jsx)("div",{className:"space-y-3 mb-8",children:["Zakwaterowanie w boutique hotels","Wszystkie posiłki (local cuisine)","Daily joga i medytacja","Autentyczna ayurveda","Transport prywatny klimatyzowany","Przewodnik lokalny","Entrance fees wszystkie atrakcje","Certyfikat Sri Lanka Yoga Experience"].map((e,a)=>(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"w-6 h-6 bg-temple/10 rounded-full flex items-center justify-center flex-shrink-0",children:(0,s.jsx)(y.A,{className:"w-4 h-4 text-temple"})}),(0,s.jsx)("span",{className:"text-wood",children:e})]},a))}),(0,s.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-lg p-6 border border-temple/20",children:[(0,s.jsx)("h3",{className:"font-semibold text-charcoal mb-3",children:"Cena retreatu:"}),(0,s.jsxs)("div",{className:"flex items-center gap-4 mb-3",children:[(0,s.jsx)("span",{className:"text-3xl font-bold text-temple",children:"3800 PLN"}),(0,s.jsx)(c.Badge,{className:"bg-golden/20 text-golden",children:"Early Bird -200 PLN"})]}),(0,s.jsx)("p",{className:"text-sm text-wood",children:"Cena za osobę, pok\xf3j podw\xf3jny. Dopłata single: 400 PLN"})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("div",{className:"relative aspect-square rounded-lg overflow-hidden",children:(0,s.jsx)(n.default,{src:"/images/destinations/sigiriya-sunrise.webp",alt:"Sigiriya sunrise yoga",fill:!0,className:"object-cover"})}),(0,s.jsx)("div",{className:"relative aspect-square rounded-lg overflow-hidden",children:(0,s.jsx)(n.default,{src:"/images/destinations/kandy-temple.webp",alt:"Kandy Temple of Tooth",fill:!0,className:"object-cover"})})]}),(0,s.jsxs)("div",{className:"space-y-4 mt-8",children:[(0,s.jsx)("div",{className:"relative aspect-square rounded-lg overflow-hidden",children:(0,s.jsx)(n.default,{src:"/images/destinations/ella-train.webp",alt:"Ella train ride",fill:!0,className:"object-cover"})}),(0,s.jsx)("div",{className:"relative aspect-square rounded-lg overflow-hidden",children:(0,s.jsx)(n.default,{src:"/images/destinations/ayurveda-sri-lanka.webp",alt:"Ayurveda treatment Sri Lanka",fill:!0,className:"object-cover"})})]})]})]})})}),(0,s.jsx)("section",{className:"py-16 bg-gradient-to-r from-temple/10 to-golden/10",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto px-4 text-center",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-charcoal mb-4",children:"Odkryj magię Sri Lanka z jogi"}),(0,s.jsx)("p",{className:"text-wood mb-8 max-w-2xl mx-auto",children:"Dołącz do nas na wyjątkowym retreecie jogi na Sri Lanka. Limitowane miejsca - tylko 10 os\xf3b!"}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center mb-8",children:[(0,s.jsx)(o.Button,{size:"lg",className:"bg-temple hover:bg-temple/90 text-lg px-8 py-6",asChild:!0,children:(0,s.jsxs)(i(),{href:"/rezerwacja",children:["Zarezerwuj Sri Lanka",(0,s.jsx)(h.A,{className:"w-5 h-5 ml-2"})]})}),(0,s.jsx)(o.Button,{size:"lg",variant:"outline",className:"border-temple text-temple hover:bg-temple/10 text-lg px-8 py-6",asChild:!0,children:(0,s.jsx)(i(),{href:"/kontakt",children:"Zadaj Pytanie"})})]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row items-center justify-center gap-6 text-wood",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(b.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"+48 666 777 888"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(f.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"<EMAIL>"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(v.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"@fly_with_bakasana"})]})]})]})}),(0,s.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify({"@context":"https://schema.org","@type":"TouristTrip",name:"Joga Sri Lanka Retreat 2025 - BAKASANA",description:"Transformacyjne retreaty jogi na Sri Lanka z Julią Jakubowicz. Sigiriya, Kandy, Ella, ayurveda, perła Oceanu Indyjskiego.",url:"https://bakasana-travel.blog/joga-sri-lanka-retreat",image:"https://bakasana-travel.blog/images/destinations/sigiriya-hero.webp",duration:"P10D",startDate:"2025-03-01",endDate:"2025-12-31",offers:{"@type":"Offer",price:"3800",priceCurrency:"PLN",availability:"https://schema.org/InStock",url:"https://bakasana-travel.blog/rezerwacja"},provider:{"@type":"TravelAgency",name:"BAKASANA",url:"https://bakasana-travel.blog"}})}})]})}},84594:(e,a,t)=>{Promise.resolve().then(t.t.bind(t,85814,23)),Promise.resolve().then(t.t.bind(t,46533,23)),Promise.resolve().then(t.bind(t,62820)),Promise.resolve().then(t.bind(t,76717))},86810:(e,a,t)=>{"use strict";t.d(a,{Badge:()=>r});var s=t(12907);let r=(0,s.registerClientReference)(function(){throw Error("Attempted to call Badge() from the server but Badge is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\badge.jsx","Badge");(0,s.registerClientReference)(function(){throw Error("Attempted to call badgeVariants() from the server but badgeVariants is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\components\\ui\\badge.jsx","badgeVariants")},98629:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>m,tree:()=>c});var s=t(65239),r=t(48088),i=t(88170),n=t.n(i),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(a,l);let c={children:["",{children:["joga-sri-lanka-retreat",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,81523)),"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\joga-sri-lanka-retreat\\page.jsx"]}]},{metadata:{icon:[],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,47103))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,39769)),"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\layout.jsx"],error:[()=>Promise.resolve().then(t.bind(t,42201)),"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\error.jsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,43867)),"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\not-found.jsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,47103))).default(e)],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\joga-sri-lanka-retreat\\page.jsx"],p={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/joga-sri-lanka-retreat/page",pathname:"/joga-sri-lanka-retreat",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})}};var a=require("../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),s=a.X(0,[4447,4693,6533,3384,8947,2697],()=>t(98629));module.exports=s})();