(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3698],{40550:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});var l=t(95155),a=t(12115),i=t(35695);function r(){let[e,s]=(0,a.useState)(!1),[t,r]=(0,a.useState)(""),[n,d]=(0,a.useState)(""),[c,o]=(0,a.useState)(!0),m=(0,i.useRouter)();(0,a.useEffect)(()=>{let e=localStorage.getItem("admin-token");e?x(e):o(!1)},[]);let x=async e=>{try{(await fetch("/api/admin/verify",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${e}`}})).ok?s(!0):localStorage.removeItem("admin-token")}catch(e){localStorage.removeItem("admin-token")}o(!1)},h=async e=>{e.preventDefault(),d("");try{let e=await fetch("/api/admin/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({password:t})}),l=await e.json();e.ok?(localStorage.setItem("admin-token",l.token),s(!0)):d(l.error||"Nieprawidłowe hasło")}catch(e){d("Błąd połączenia")}};return c?(0,l.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-temple mx-auto mb-4"}),(0,l.jsx)("p",{className:"text-temple",children:"Ładowanie..."})]})}):e?(0,l.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-rice to-mist",children:[(0,l.jsx)("header",{className:"bg-white shadow-sm border-b border-temple/10",children:(0,l.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,l.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,l.jsx)("div",{className:"flex items-center",children:(0,l.jsx)("h1",{className:"text-xl font-serif text-temple",children:"Panel Administracyjny"})}),(0,l.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,l.jsx)("span",{className:"text-sm text-wood-light",children:"Zalogowany jako Administrator"}),(0,l.jsx)("button",{onClick:()=>{localStorage.removeItem("admin-token"),s(!1),r("")},className:"text-sm text-temple hover:text-temple/70 transition-colors",children:"Wyloguj się"})]})]})})}),(0,l.jsxs)("main",{className:"max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,l.jsxs)("div",{className:"bg-white rounded-xl shadow-soft p-6",children:[(0,l.jsxs)("div",{className:"flex items-center mb-4",children:[(0,l.jsx)("div",{className:"w-12 h-12 bg-temple/10 rounded-lg flex items-center justify-center mr-4",children:(0,l.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCDD"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-lg font-medium text-temple",children:"Sanity CMS"}),(0,l.jsx)("p",{className:"text-sm text-wood-light",children:"Zarządzaj treścią"})]})]}),(0,l.jsx)("p",{className:"text-wood-light text-sm mb-4",children:"Edytuj retreaty, opinie, FAQ i artykuły bloga"}),(0,l.jsxs)("a",{href:"https://bakasana-travel.sanity.studio",target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center px-4 py-2 bg-temple text-white text-sm rounded-lg hover:bg-temple/90 transition-colors",children:["Otw\xf3rz CMS",(0,l.jsx)("span",{className:"ml-2",children:"→"})]})]}),(0,l.jsxs)("div",{className:"bg-white rounded-xl shadow-soft p-6",children:[(0,l.jsxs)("div",{className:"flex items-center mb-4",children:[(0,l.jsx)("div",{className:"w-12 h-12 bg-golden/10 rounded-lg flex items-center justify-center mr-4",children:(0,l.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCA"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-lg font-medium text-temple",children:"Analytics"}),(0,l.jsx)("p",{className:"text-sm text-wood-light",children:"Statystyki strony"})]})]}),(0,l.jsx)("p",{className:"text-wood-light text-sm mb-4",children:"Zobacz raporty odwiedzin i konwersji"}),(0,l.jsxs)("a",{href:"https://analytics.google.com",target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center px-4 py-2 bg-golden text-white text-sm rounded-lg hover:bg-golden/90 transition-colors",children:["Google Analytics",(0,l.jsx)("span",{className:"ml-2",children:"→"})]})]}),(0,l.jsxs)("div",{className:"bg-white rounded-xl shadow-soft p-6",children:[(0,l.jsxs)("div",{className:"flex items-center mb-4",children:[(0,l.jsx)("div",{className:"w-12 h-12 bg-sage/10 rounded-lg flex items-center justify-center mr-4",children:(0,l.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCC5"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-lg font-medium text-temple",children:"Rezerwacje"}),(0,l.jsx)("p",{className:"text-sm text-wood-light",children:"Zarządzaj bookingami"})]})]}),(0,l.jsx)("p",{className:"text-wood-light text-sm mb-4",children:"Przeglądaj i zarządzaj rezerwacjami"}),(0,l.jsxs)("button",{onClick:()=>m.push("/admin/bookings"),className:"inline-flex items-center px-4 py-2 bg-sage text-white text-sm rounded-lg hover:bg-sage/90 transition-colors",children:["Zobacz rezerwacje",(0,l.jsx)("span",{className:"ml-2",children:"→"})]})]}),(0,l.jsxs)("div",{className:"bg-white rounded-xl shadow-soft p-6",children:[(0,l.jsxs)("div",{className:"flex items-center mb-4",children:[(0,l.jsx)("div",{className:"w-12 h-12 bg-lotus/10 rounded-lg flex items-center justify-center mr-4",children:(0,l.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCE7"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-lg font-medium text-temple",children:"Newsletter"}),(0,l.jsx)("p",{className:"text-sm text-wood-light",children:"Zarządzaj subskrypcjami"})]})]}),(0,l.jsx)("p",{className:"text-wood-light text-sm mb-4",children:"Przeglądaj subskrybent\xf3w i wysyłaj kampanie"}),(0,l.jsxs)("button",{onClick:()=>m.push("/admin/newsletter"),className:"inline-flex items-center px-4 py-2 bg-lotus text-white text-sm rounded-lg hover:bg-lotus/90 transition-colors",children:["Zarządzaj newsletter",(0,l.jsx)("span",{className:"ml-2",children:"→"})]})]}),(0,l.jsxs)("div",{className:"bg-white rounded-xl shadow-soft p-6",children:[(0,l.jsxs)("div",{className:"flex items-center mb-4",children:[(0,l.jsx)("div",{className:"w-12 h-12 bg-wood/10 rounded-lg flex items-center justify-center mr-4",children:(0,l.jsx)("span",{className:"text-2xl",children:"⚙️"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-lg font-medium text-temple",children:"Ustawienia"}),(0,l.jsx)("p",{className:"text-sm text-wood-light",children:"Konfiguracja strony"})]})]}),(0,l.jsx)("p",{className:"text-wood-light text-sm mb-4",children:"Zarządzaj ustawieniami i konfiguracją"}),(0,l.jsxs)("button",{onClick:()=>m.push("/admin/settings"),className:"inline-flex items-center px-4 py-2 bg-wood text-white text-sm rounded-lg hover:bg-wood/90 transition-colors",children:["Ustawienia",(0,l.jsx)("span",{className:"ml-2",children:"→"})]})]})]}),(0,l.jsxs)("div",{className:"mt-8 bg-white rounded-xl shadow-soft p-6",children:[(0,l.jsx)("h3",{className:"text-lg font-medium text-temple mb-4",children:"Szybkie statystyki"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,l.jsxs)("div",{className:"text-center p-4 bg-temple/5 rounded-lg",children:[(0,l.jsx)("div",{className:"text-2xl font-bold text-temple",children:"-"}),(0,l.jsx)("div",{className:"text-sm text-wood-light",children:"Rezerwacje"})]}),(0,l.jsxs)("div",{className:"text-center p-4 bg-golden/5 rounded-lg",children:[(0,l.jsx)("div",{className:"text-2xl font-bold text-temple",children:"-"}),(0,l.jsx)("div",{className:"text-sm text-wood-light",children:"Newsletter"})]}),(0,l.jsxs)("div",{className:"text-center p-4 bg-sage/5 rounded-lg",children:[(0,l.jsx)("div",{className:"text-2xl font-bold text-temple",children:"-"}),(0,l.jsx)("div",{className:"text-sm text-wood-light",children:"Odwiedziny"})]}),(0,l.jsxs)("div",{className:"text-center p-4 bg-lotus/5 rounded-lg",children:[(0,l.jsx)("div",{className:"text-2xl font-bold text-temple",children:"-"}),(0,l.jsx)("div",{className:"text-sm text-wood-light",children:"Konwersje"})]})]})]})]})]}):(0,l.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-rice to-mist",children:(0,l.jsxs)("div",{className:"bg-white p-8 rounded-2xl shadow-soft max-w-md w-full mx-4",children:[(0,l.jsxs)("div",{className:"text-center mb-8",children:[(0,l.jsx)("h1",{className:"text-2xl font-serif text-temple mb-2",children:"Panel Administracyjny"}),(0,l.jsx)("p",{className:"text-wood-light",children:"Bakasana Travel Blog"})]}),(0,l.jsxs)("form",{onSubmit:h,className:"space-y-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-temple mb-2",children:"Hasło administratora"}),(0,l.jsx)("input",{type:"password",id:"password",value:t,onChange:e=>r(e.target.value),className:"w-full px-4 py-3 border border-temple/20 rounded-lg focus:ring-2 focus:ring-temple/20 focus:border-temple transition-colors",placeholder:"Wprowadź hasło",required:!0})]}),n&&(0,l.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm",children:n}),(0,l.jsx)("button",{type:"submit",className:"w-full bg-temple text-white py-3 px-4 rounded-lg hover:bg-temple/90 transition-colors font-medium",children:"Zaloguj się"})]}),(0,l.jsx)("div",{className:"mt-6 text-center",children:(0,l.jsx)("p",{className:"text-xs text-wood-light",children:"\uD83D\uDD12 Zabezpieczone połączenie"})})]})})}},93460:(e,s,t)=>{Promise.resolve().then(t.bind(t,40550))}},e=>{var s=s=>e(e.s=s);e.O(0,[8096,7358],()=>s(93460)),_N_E=e.O()}]);