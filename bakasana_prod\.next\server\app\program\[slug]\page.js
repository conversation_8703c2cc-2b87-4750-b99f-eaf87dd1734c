(()=>{var e={};e.id=1543,e.ids=[1543],e.modules={3217:(e,r,a)=>{"use strict";a.r(r),a.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>c,tree:()=>p});var s=a(65239),t=a(48088),o=a(88170),n=a.n(o),i=a(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);a.d(r,d);let p={children:["",{children:["program",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,45069)),"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\program\\[slug]\\page.jsx"]}]},{}]},{metadata:{icon:[],apple:[],openGraph:[async e=>(await Promise.resolve().then(a.bind(a,47103))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,39769)),"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\layout.jsx"],error:[()=>Promise.resolve().then(a.bind(a,42201)),"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\error.jsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,43867)),"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\not-found.jsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[],apple:[],openGraph:[async e=>(await Promise.resolve().then(a.bind(a,47103))).default(e)],twitter:[],manifest:void 0}}]}.children,l=["C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\program\\[slug]\\page.jsx"],u={require:a,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/program/[slug]/page",pathname:"/program/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},45069:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>t});var s=a(37413);function t({params:e}){return(0,s.jsx)("div",{children:(0,s.jsxs)("h1",{children:["Program wycieczki: ",e.slug]})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var a=e=>r(r.s=e),s=r.X(0,[4447,4693,2697],()=>a(3217));module.exports=s})();