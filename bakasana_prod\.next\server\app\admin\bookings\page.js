(()=>{var e={};e.id=6907,e.ids=[6907],e.modules={1326:(e,t,s)=>{Promise.resolve().then(s.bind(s,87319))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},47981:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>x,tree:()=>o});var a=s(65239),r=s(48088),n=s(88170),i=s.n(n),l=s(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(t,d);let o={children:["",{children:["admin",{children:["bookings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,83885)),"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\admin\\bookings\\page.jsx"]}]},{}]},{metadata:{icon:[],apple:[],openGraph:[async e=>(await Promise.resolve().then(s.bind(s,47103))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,39769)),"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\layout.jsx"],error:[()=>Promise.resolve().then(s.bind(s,42201)),"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\error.jsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,43867)),"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\not-found.jsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[],apple:[],openGraph:[async e=>(await Promise.resolve().then(s.bind(s,47103))).default(e)],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\admin\\bookings\\page.jsx"],p={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/bookings/page",pathname:"/admin/bookings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},83174:(e,t,s)=>{Promise.resolve().then(s.bind(s,83885))},83885:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\admin\\\\bookings\\\\page.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\admin\\bookings\\page.jsx","default")},87319:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var a=s(60687),r=s(43210),n=s(16189);function i(){let[e,t]=(0,r.useState)(!1),[s,i]=(0,r.useState)([]),[l,d]=(0,r.useState)(!0),[o,c]=(0,r.useState)("all"),p=(0,n.useRouter)(),x=async()=>{try{let e=await fetch("/api/admin/bookings");if(e.ok){let t=await e.json();i(t.bookings||[])}}catch(e){}},m=async(e,t)=>{try{let s=localStorage.getItem("admin-token");(await fetch(`/api/admin/bookings/${e}`,{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:`Bearer ${s}`},body:JSON.stringify({status:t})})).ok&&await x()}catch(e){}},h=e=>{switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"confirmed":return"bg-green-100 text-green-800";case"cancelled":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},u=e=>{switch(e){case"pending":return"Oczekuje";case"confirmed":return"Potwierdzona";case"cancelled":return"Anulowana";default:return"Nieznany"}},j=s.filter(e=>"all"===o||e.status===o);return l?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-temple mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-temple",children:"Ładowanie..."})]})}):e?(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-rice to-mist",children:[(0,a.jsx)("header",{className:"bg-white shadow-sm border-b border-temple/10",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("button",{onClick:()=>p.push("/admin"),className:"text-temple hover:text-temple/70 mr-4",children:"← Powr\xf3t"}),(0,a.jsx)("h1",{className:"text-xl font-serif text-temple",children:"Zarządzanie Rezerwacjami"})]}),(0,a.jsx)("div",{className:"flex items-center space-x-4",children:(0,a.jsxs)("span",{className:"text-sm text-wood-light",children:[j.length," rezerwacji"]})})]})})}),(0,a.jsxs)("main",{className:"max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-soft p-6 mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-temple mb-4",children:"Filtry"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:[{key:"all",label:"Wszystkie",count:s.length},{key:"pending",label:"Oczekujące",count:s.filter(e=>"pending"===e.status).length},{key:"confirmed",label:"Potwierdzone",count:s.filter(e=>"confirmed"===e.status).length},{key:"cancelled",label:"Anulowane",count:s.filter(e=>"cancelled"===e.status).length}].map(e=>(0,a.jsxs)("button",{onClick:()=>c(e.key),className:`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${o===e.key?"bg-temple text-white":"bg-temple/10 text-temple hover:bg-temple/20"}`,children:[e.label," (",e.count,")"]},e.key))})]}),(0,a.jsx)("div",{className:"bg-white rounded-xl shadow-soft overflow-hidden",children:0===j.length?(0,a.jsxs)("div",{className:"p-8 text-center",children:[(0,a.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDCC5"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-temple mb-2",children:"Brak rezerwacji"}),(0,a.jsx)("p",{className:"text-wood-light",children:"all"===o?"Nie ma jeszcze żadnych rezerwacji.":`Nie ma rezerwacji o statusie "${u(o)}".`})]}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-temple/10",children:[(0,a.jsx)("thead",{className:"bg-temple/5",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-temple uppercase tracking-wider",children:"Klient"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-temple uppercase tracking-wider",children:"Program"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-temple uppercase tracking-wider",children:"Data"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-temple uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-temple uppercase tracking-wider",children:"Akcje"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-temple/10",children:j.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-temple/5",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"text-sm font-medium text-temple",children:[e.firstName," ",e.lastName]}),(0,a.jsx)("div",{className:"text-sm text-wood-light",children:e.email}),(0,a.jsx)("div",{className:"text-sm text-wood-light",children:e.phone})]})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsx)("div",{className:"text-sm text-temple",children:e.program}),(0,a.jsx)("div",{className:"text-sm text-wood-light",children:e.destination})]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsx)("div",{className:"text-sm text-temple",children:new Date(e.createdAt).toLocaleDateString("pl-PL")}),(0,a.jsx)("div",{className:"text-sm text-wood-light",children:new Date(e.createdAt).toLocaleTimeString("pl-PL")})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${h(e.status)}`,children:u(e.status)})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:["pending"===e.status&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("button",{onClick:()=>m(e.id,"confirmed"),className:"text-green-600 hover:text-green-900",children:"Potwierdź"}),(0,a.jsx)("button",{onClick:()=>m(e.id,"cancelled"),className:"text-red-600 hover:text-red-900",children:"Anuluj"})]}),"confirmed"===e.status&&(0,a.jsx)("button",{onClick:()=>m(e.id,"cancelled"),className:"text-red-600 hover:text-red-900",children:"Anuluj"}),"cancelled"===e.status&&(0,a.jsx)("button",{onClick:()=>m(e.id,"pending"),className:"text-blue-600 hover:text-blue-900",children:"Przywr\xf3ć"})]})})]},e.id))})]})})})]})]}):null}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[4447,4693,2697],()=>s(47981));module.exports=a})();