(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9937],{18609:(e,a,t)=>{Promise.resolve().then(t.bind(t,55522)),Promise.resolve().then(t.bind(t,88994))},55522:(e,a,t)=>{"use strict";t.d(a,{default:()=>o});var s=t(95155),r=t(12115);function o(){let[e,a]=(0,r.useState)({name:"",email:"",phone:"",message:"",retreatInterest:"",honeypot:""}),[t,o]=(0,r.useState)(""),[n,l]=(0,r.useState)(!1),[i,c]=(0,r.useState)({}),[m,d]=(0,r.useState)({}),h=(e,a)=>{switch(e){case"name":return a.length<2?"Imię musi mieć co najmniej 2 znaki":"";case"email":return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(a)?"":"Podaj prawidłowy adres email";case"phone":return a&&!/^[\+]?[0-9\s\-\(\)]{9,}$/.test(a)?"Podaj prawidłowy numer telefonu":"";case"message":return a.length<10?"Wiadomość musi mieć co najmniej 10 znak\xf3w":"";default:return""}},p=t=>{let{id:s,value:r}=t.target;if(a({...e,[s]:r}),m[s]){let e=h(s,r);c({...i,[s]:e})}},u=e=>{let{id:a,value:t}=e.target;d({...m,[a]:!0});let s=h(a,t);c({...i,[a]:s})},b=async t=>{if(t.preventDefault(),e.honeypot)return;let s={};if(Object.keys(e).forEach(a=>{if("honeypot"!==a&&"phone"!==a&&"retreatInterest"!==a){let t=h(a,e[a]);t&&(s[a]=t)}}),Object.keys(s).length>0){c(s),d(Object.keys(e).reduce((e,a)=>({...e,[a]:!0}),{})),o("Proszę poprawić błędy w formularzu");return}l(!0),o("Wysyłanie...");try{let t=await fetch("https://api.web3forms.com/submit",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify({access_key:"your-web3forms-access-key",name:e.name,email:e.email,phone:e.phone,message:e.message,retreat_interest:e.retreatInterest,subject:`Nowa wiadomość z BAKASANA od ${e.name}`,from_name:"BAKASANA",to_email:"<EMAIL>",source:"contact_form",timestamp:new Date().toISOString(),user_agent:navigator.userAgent})}),s=await t.json();if(s.success)o("✅ Wiadomość wysłana pomyślnie! Odpowiemy w ciągu 24 godzin."),a({name:"",email:"",phone:"",message:"",retreatInterest:"",honeypot:""}),c({}),d({}),window.gtag&&window.gtag("event","form_submit",{event_category:"Contact",event_label:"Contact Form Success"});else throw Error(s.message||"Błąd wysyłania")}catch(e){o("Wystąpił błąd. Spr\xf3buj ponownie lub napisz bezpoś<NAME_EMAIL>")}finally{l(!1),setTimeout(()=>o(""),8e3)}};return(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-20 items-start max-w-5xl mx-auto",children:[(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{className:"text-center lg:text-left",children:[(0,s.jsx)("h2",{className:"section-header mb-6",children:"Napisz do nas"}),(0,s.jsx)("p",{className:"body-text opacity-80",children:"Każda wiadomość jest dla nas ważna"})]}),(0,s.jsxs)("form",{onSubmit:b,className:"space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"name",className:"block subtle-text mb-3",children:"Imię *"}),(0,s.jsx)("input",{type:"text",id:"name",value:e.name,onChange:p,onBlur:u,required:!0,className:`w-full px-0 py-4 bg-transparent border-0 border-b transition-colors text-charcoal placeholder-stone/50 ${i.name?"border-red-500":"border-stone/30 focus:border-temple-gold"} focus:outline-none`,placeholder:"Twoje imię","aria-describedby":i.name?"name-error":void 0}),i.name&&(0,s.jsx)("p",{id:"name-error",className:"text-red-500 text-sm mt-2",children:i.name})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block subtle-text mb-3",children:"Email *"}),(0,s.jsx)("input",{type:"email",id:"email",value:e.email,onChange:p,onBlur:u,required:!0,className:`w-full px-0 py-4 bg-transparent border-0 border-b transition-colors text-charcoal placeholder-stone/50 ${i.email?"border-red-500":"border-stone/30 focus:border-temple-gold"} focus:outline-none`,placeholder:"<EMAIL>","aria-describedby":i.email?"email-error":void 0}),i.email&&(0,s.jsx)("p",{id:"email-error",className:"text-red-500 text-sm mt-2",children:i.email})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"phone",className:"block subtle-text mb-3",children:"Telefon (opcjonalnie)"}),(0,s.jsx)("input",{type:"tel",id:"phone",value:e.phone,onChange:p,onBlur:u,className:`w-full px-0 py-4 bg-transparent border-0 border-b transition-colors text-charcoal placeholder-stone/50 ${i.phone?"border-red-500":"border-stone/30 focus:border-temple-gold"} focus:outline-none`,placeholder:"+48 123 456 789","aria-describedby":i.phone?"phone-error":void 0}),i.phone&&(0,s.jsx)("p",{id:"phone-error",className:"text-red-500 text-sm mt-2",children:i.phone})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"retreatInterest",className:"block subtle-text mb-3",children:"Interesuje Cię (opcjonalnie)"}),(0,s.jsxs)("select",{id:"retreatInterest",value:e.retreatInterest,onChange:p,className:"w-full px-0 py-4 bg-transparent border-0 border-b border-stone/30 focus:border-temple-gold focus:outline-none transition-colors text-charcoal",children:[(0,s.jsx)("option",{value:"",children:"Wybierz opcję"}),(0,s.jsx)("option",{value:"retreat-bali",children:"Retreat na Bali"}),(0,s.jsx)("option",{value:"retreat-poland",children:"Retreat w Polsce"}),(0,s.jsx)("option",{value:"private-sessions",children:"Sesje indywidualne"}),(0,s.jsx)("option",{value:"online-classes",children:"Zajęcia online"}),(0,s.jsx)("option",{value:"workshops",children:"Warsztaty"}),(0,s.jsx)("option",{value:"other",children:"Inne"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"message",className:"block subtle-text mb-3",children:"Wiadomość *"}),(0,s.jsx)("textarea",{id:"message",value:e.message,onChange:p,onBlur:u,required:!0,rows:6,className:`w-full px-0 py-4 bg-transparent border-0 border-b transition-colors text-charcoal placeholder-stone/50 resize-none ${i.message?"border-red-500":"border-stone/30 focus:border-temple-gold"} focus:outline-none`,placeholder:"Podziel się swoimi myślami...","aria-describedby":i.message?"message-error":void 0}),i.message&&(0,s.jsx)("p",{id:"message-error",className:"text-red-500 text-sm mt-2",children:i.message})]}),(0,s.jsx)("input",{type:"text",id:"honeypot",name:"honeypot",value:e.honeypot,onChange:p,style:{display:"none"},tabIndex:"-1",autoComplete:"off"}),(0,s.jsxs)("div",{className:"pt-8",children:[(0,s.jsx)("button",{type:"submit",disabled:n,className:`btn-ghost btn-primary ${n?"opacity-50 cursor-not-allowed":""}`,children:n?"Wysyłanie...":"Wyślij Wiadomość"}),t&&(0,s.jsx)("p",{className:"text-sm text-charcoal/70 font-light mt-4 max-w-xs",children:t})]})]})]}),(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{className:"text-center lg:text-left",children:[(0,s.jsx)("h3",{className:"section-header mb-6",children:"Znajdź nas"}),(0,s.jsx)("p",{className:"body-text opacity-80 mb-8",children:"Połączmy się w przestrzeni cyfrowej"})]}),(0,s.jsx)("div",{className:"space-y-6",children:[{href:"https://www.instagram.com/fly_with_bakasana?igsh=MWpmanNpeHVodTlubA%3D%3D&utm_source=qr",label:"Instagram",aria:"Profil na Instagramie"},{href:"https://www.facebook.com/p/Fly-with-bakasana-100077568306563/",label:"Facebook",aria:"Profil na Facebooku"},{href:"mailto:<EMAIL>",label:"Email",aria:"Kontakt email"}].map(e=>(0,s.jsxs)("a",{href:e.href,target:"_blank",rel:"noopener noreferrer","aria-label":e.aria,className:"block p-6 hover:opacity-70 transition-opacity duration-200 text-center lg:text-left",children:[(0,s.jsx)("h4",{className:"font-light text-charcoal mb-2 tracking-wide text-lg",children:e.label}),(0,s.jsxs)("p",{className:"text-sm text-stone font-light",children:["Instagram"===e.label&&"Codzienne inspiracje","Facebook"===e.label&&"Społeczność BAKASANA","Email"===e.label&&"Bezpośredni kontakt"]})]},e.label))}),(0,s.jsx)("div",{className:"flex items-center justify-center lg:justify-start my-12",children:(0,s.jsxs)("div",{className:"flex items-center gap-4 text-temple-gold/60",children:[(0,s.jsx)("div",{className:"w-12 h-px bg-temple-gold/30"}),(0,s.jsx)("span",{className:"text-lg opacity-60",children:"ॐ"}),(0,s.jsx)("div",{className:"w-12 h-px bg-temple-gold/30"})]})}),(0,s.jsxs)("div",{className:"text-center lg:text-left",children:[(0,s.jsx)("p",{className:"text-sm text-stone font-light italic tracking-wide",children:'"Każda podr\xf3ż zaczyna się od jednego kroku..."'}),(0,s.jsx)("p",{className:"text-xs text-temple-gold font-light tracking-wide uppercase mt-2",children:"Om Swastiastu"})]})]})]})}},88994:(e,a,t)=>{"use strict";t.d(a,{default:()=>l});var s=t(95155),r=t(12115);let o=(0,r.memo)(e=>{let{className:a="w-5 h-5"}=e;return(0,s.jsx)("svg",{className:a,fill:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",children:(0,s.jsx)("path",{d:"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"})})});o.displayName="WhatsAppIcon";let n=(0,r.memo)(e=>{let{className:a="",size:t="md",message:n="Cześć! Interesuję się retreatami jogowymi na Bali lub Sri Lance. Czy możesz mi przesłać więcej informacji?",variant:l="button"}=e,i=(0,r.useCallback)(()=>{window.gtag&&window.gtag("event","whatsapp_click",{event_category:"engagement",event_label:"whatsapp_contact",value:1}),window.fbq&&window.fbq("track","Contact")},[]),c={sm:"w-4 h-4",md:"w-5 h-5",lg:"w-6 h-6"},m=`https://wa.me/48606101523?text=${encodeURIComponent(n)}`;return"icon"===l?(0,s.jsx)("a",{href:m,target:"_blank",rel:"noopener noreferrer",onClick:i,className:`text-enterprise-brown hover:text-terra transition-colors duration-300 ${a}`,"aria-label":"Skontaktuj się przez WhatsApp",children:(0,s.jsx)(o,{className:c[t]})}):"floating"===l?(0,s.jsx)("div",{className:"fixed bottom-6 right-6 z-50",children:(0,s.jsx)("a",{href:m,target:"_blank",rel:"noopener noreferrer",onClick:i,className:`
            bg-green-500 hover:bg-green-600 text-white 
            w-14 h-14 rounded-full
            transition-all duration-300 
            hover:scale-110 focus:outline-none focus:ring-2 focus:ring-green-500/50
            focus:ring-offset-2 shadow-lg hover:shadow-xl
            flex items-center justify-center
            animate-pulse hover:animate-none
            ${a}
          `,"aria-label":"Skontaktuj się przez WhatsApp",children:(0,s.jsx)(o,{className:"w-6 h-6"})})}):(0,s.jsx)("a",{href:m,target:"_blank",rel:"noopener noreferrer",onClick:i,className:`
        bg-enterprise-brown hover:bg-enterprise-brown/90 text-white 
        ${{sm:"h-10 w-10",md:"h-12 w-12",lg:"h-14 w-14"}[t]} rounded-full
        transition-all duration-300 
        hover:scale-105 focus:outline-none focus:ring-2 focus:ring-enterprise-brown/50
        focus:ring-offset-2 shadow-lg hover:shadow-xl
        flex items-center justify-center
        ${a}
      `,"aria-label":"Skontaktuj się przez WhatsApp",children:(0,s.jsx)(o,{className:c[t]})})});n.displayName="PerformantWhatsApp";let l=n}},e=>{var a=a=>e(e.s=a);e.O(0,[8096,7358],()=>a(18609)),_N_E=e.O()}]);