(()=>{var e={};e.id=1046,e.ids=[1046],e.modules={2556:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\o-mnie\\\\page.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\o-mnie\\page.jsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},18097:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>p,routeModule:()=>m,tree:()=>d});var r=a(65239),i=a(48088),o=a(88170),n=a.n(o),s=a(30893),l={};for(let e in s)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>s[e]);a.d(t,l);let d={children:["",{children:["o-mnie",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,2556)),"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\o-mnie\\page.jsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,27531)),"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\o-mnie\\layout.jsx"],metadata:{icon:[],apple:[],openGraph:[async e=>(await Promise.resolve().then(a.bind(a,47103))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,39769)),"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\layout.jsx"],error:[()=>Promise.resolve().then(a.bind(a,42201)),"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\error.jsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,43867)),"C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\not-found.jsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[],apple:[],openGraph:[async e=>(await Promise.resolve().then(a.bind(a,47103))).default(e)],twitter:[],manifest:void 0}}]}.children,p=["C:\\Users\\<USER>\\Desktop\\Projekty\\bakasana_prod\\bakasana_prod\\src\\app\\o-mnie\\page.jsx"],c={require:a,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/o-mnie/page",pathname:"/o-mnie",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27531:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>i,metadata:()=>r});let r={title:"O mnie - Julia Jakubowicz | Certyfikowana Instruktorka Jogi | BAKASANA",description:"Poznaj Julię Jakubowicz - certyfikowaną instruktorkę jogi RYT 500, fizjoterapeutkę i przewodniczkę duchowych podr\xf3ży na Bali i Sri Lanka.",keywords:["julia jakubowicz","instruktorka jogi","RYT 500","fizjoterapeutka","bali","sri lanka","retreaty jogi"],openGraph:{title:"O mnie - Julia Jakubowicz | BAKASANA",description:"Certyfikowana instruktorka jogi i fizjoterapeutka z pasją do duchowych podr\xf3ży",images:["/images/profile/omnie-opt.webp"],type:"profile"}};function i({children:e}){return e}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30474:(e,t,a)=>{"use strict";a.d(t,{default:()=>i.a});var r=a(31261),i=a.n(r)},31261:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{default:function(){return l},getImageProps:function(){return s}});let r=a(59630),i=a(44953),o=a(46533),n=r._(a(1933));function s(e){let{props:t}=(0,i.getImgProps)(e,{defaultLoader:n.default,imgConf:{deviceSizes:[480,640,768,1024,1200,1920],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,a]of Object.entries(t))void 0===a&&delete t[e];return{props:t}}let l=o.Image},33873:e=>{"use strict";e.exports=require("path")},38846:(e,t,a)=>{Promise.resolve().then(a.bind(a,93551))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72169:(e,t,a)=>{"use strict";a.d(t,{XD:()=>o,aU:()=>n,wf:()=>i});var r=a(43210);function i(){let[e,t]=(0,r.useState)(!0),[a,i]=(0,r.useState)(!1),[o,n]=(0,r.useState)(!1),[s,l]=(0,r.useState)(!1);return s?{isDesktop:e,isTablet:a,isMobile:o}:{isDesktop:!0,isTablet:!1,isMobile:!1}}let o=(e,t,a)=>({hero:{height:"100vh",minHeight:"600px",backgroundColor:"#FDFAF7",display:"flex",alignItems:"center",justifyContent:"center",position:"relative",backgroundImage:`
      radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.02) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(0, 0, 0, 0.01) 0%, transparent 50%)
    `},heroContent:{textAlign:"center",transform:"translateY(-30px)",padding:a?"0 20px":"0"},heroTitle:{fontSize:a?"56px":t?"72px":"110px",fontFamily:"Cormorant Garamond",fontWeight:300,color:"#3D3A37",letterSpacing:"0.08em",marginBottom:"40px",lineHeight:"1.1"},heroSubtitle:{fontSize:"15px",fontFamily:"Inter",fontWeight:300,color:"#6B6560",letterSpacing:"0.15em",opacity:.8,marginBottom:0},heroMeta:{position:"absolute",bottom:"15%",left:"50%",transform:"translateX(-50%)",textAlign:"center",fontSize:"12px",fontFamily:"Inter",fontWeight:300,color:"#8B857F",letterSpacing:"0.2em",margin:0}}),n=(e,t,a)=>({section:{marginTop:a?"60px":"100px",maxWidth:"1200px",margin:`${a?"60px":"100px"} auto 0`,padding:a?"0 40px":t?"0 60px":"0 80px"},grid:{display:"grid",gridTemplateColumns:a?"1fr":"55% 45%",gap:a?"40px":t?"60px":"100px",alignItems:"start"},gridReverse:{display:"grid",gridTemplateColumns:a?"1fr":"45% 55%",gap:a?"40px":t?"60px":"100px",alignItems:"start"},sectionTitle:{fontSize:a?"32px":t?"38px":"48px",fontFamily:"Cormorant Garamond",fontWeight:300,color:"#3D3A37",letterSpacing:"0.02em",marginBottom:"36px",lineHeight:"1.2"},imageContainer:{height:a?"300px":"580px",position:"relative",aspectRatio:"3/4"},visualElement:{height:a?"300px":"480px",backgroundColor:"rgba(250, 248, 245, 0.3)",display:"flex",alignItems:"center",justifyContent:"center",position:"relative"},ctaSection:{marginTop:a?"60px":"100px",textAlign:"center",maxWidth:"600px",margin:`${a?"60px":"100px"} auto 0`,paddingBottom:a?"60px":"100px",padding:a?"0 40px":"0"},ctaTitle:{fontSize:a?"28px":t?"34px":"42px",fontFamily:"Cormorant Garamond",fontWeight:300,color:"#3D3A37",marginBottom:"24px",lineHeight:"1.3"},divider:{width:"40px",height:"1px",backgroundColor:"#C4A575",opacity:.25,margin:`${a?"60px":"100px"} auto`}})},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},91990:(e,t,a)=>{Promise.resolve().then(a.bind(a,2556))},93551:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>d});var r=a(60687);a(43210);var i=a(30474),o=a(85814),n=a.n(o),s=a(72169);let l=({className:e=""})=>(0,r.jsx)("section",{className:`py-20 bg-gradient-to-b from-sanctuary to-whisper ${e}`,children:(0,r.jsx)("div",{className:"container mx-auto px-4",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,r.jsx)("div",{className:"w-16 h-px bg-temple-gold mx-auto mb-8 opacity-60"}),(0,r.jsx)("h2",{className:"text-4xl md:text-5xl font-cormorant font-light text-charcoal mb-6 leading-tight",children:"Rozpocznij swoją transformację"}),(0,r.jsx)("p",{className:"text-lg text-charcoal-light leading-relaxed mb-12 max-w-2xl mx-auto font-light",children:"Skontaktuj się ze mną i odkryj głębię praktyki jogi - w przestrzeni fizycznej na retreatach lub w intymnej atmosferze zajęć online."}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center items-center",children:[(0,r.jsxs)(n(),{href:"/retreaty",className:"group inline-flex items-center px-8 py-3 bg-transparent border border-temple-gold text-temple-gold hover:bg-temple-gold hover:text-sanctuary transition-all duration-300 font-light tracking-wider text-sm uppercase hover:shadow-lg",children:["Zobacz retreaty",(0,r.jsx)("svg",{className:"ml-2 w-4 h-4 transform group-hover:translate-x-1 transition-transform duration-300",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M17 8l4 4m0 0l-4 4m4-4H3"})})]}),(0,r.jsxs)(n(),{href:"/zajecia-online",className:"group inline-flex items-center px-8 py-3 bg-transparent border border-temple-gold text-temple-gold hover:bg-temple-gold hover:text-sanctuary transition-all duration-300 font-light tracking-wider text-sm uppercase hover:shadow-lg",children:["Zajęcia online",(0,r.jsx)("svg",{className:"ml-2 w-4 h-4 transform group-hover:translate-x-1 transition-transform duration-300",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M17 8l4 4m0 0l-4 4m4-4H3"})})]}),(0,r.jsxs)(n(),{href:"/kontakt",className:"group inline-flex items-center px-8 py-3 bg-temple-gold border border-temple-gold text-sanctuary hover:bg-golden-amber hover:border-golden-amber transition-all duration-300 font-medium tracking-wider text-sm uppercase shadow-lg hover:shadow-xl hover:scale-105",children:["Skontaktuj się",(0,r.jsx)("svg",{className:"ml-2 w-4 h-4 transform group-hover:translate-x-1 transition-transform duration-300",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M17 8l4 4m0 0l-4 4m4-4H3"})})]})]}),(0,r.jsx)("div",{className:"w-16 h-px bg-temple-gold mx-auto mt-12 opacity-60"})]})})});function d(){let{isDesktop:e,isTablet:t,isMobile:a}=(0,s.wf)(),o=(0,s.aU)(e,t,a);return(0,r.jsxs)("main",{className:"bg-sanctuary min-h-screen",children:[(0,r.jsx)("section",{className:"magazine-hero",children:(0,r.jsxs)("div",{className:"magazine-hero-content",children:[(0,r.jsx)("div",{className:"magazine-header-line"}),(0,r.jsx)("h1",{className:"magazine-title",children:"Julia Jakubowicz"}),(0,r.jsx)("p",{className:"magazine-subtitle",children:"Instruktorka jogi RYT 500 • Fizjoterapeutka"}),(0,r.jsx)("div",{className:"magazine-meta",children:"Przewodniczka transformacyjnych podr\xf3ży"}),(0,r.jsx)("div",{className:"magazine-header-line"})]})}),(0,r.jsx)("section",{style:o.section,children:(0,r.jsxs)("div",{style:o.grid,children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{style:o.sectionTitle,children:"Moja droga"}),(0,r.jsx)("p",{style:{fontSize:"17px",fontFamily:"Inter",fontWeight:300,color:"#5B5754",lineHeight:"1.8",marginBottom:"32px"},children:"Prawdziwa transformacja dzieje się poza matą — w codzienności, w spos\xf3b, w jaki oddychamy, poruszamy się i odnosimy do świata."}),(0,r.jsx)("div",{style:{fontSize:"15px",fontFamily:"Inter",fontWeight:300,color:"#3D3A37",lineHeight:"1.9",letterSpacing:"0.02em",marginBottom:"56px",textAlign:"left"},children:"Bali i Sri Lanka nauczyły mnie słuchania tej prawdziwej ciszy, kt\xf3ra mieszka w sercu. Każda podr\xf3ż to powr\xf3t do naszej autentycznej natury, do tego kim naprawdę jesteśmy, gdy zdjemy wszystkie maski i społeczne oczekiwania."}),(0,r.jsxs)("div",{style:{marginBottom:"64px"},children:[(0,r.jsx)("h3",{style:{fontSize:"20px",fontFamily:"Cormorant Garamond",fontWeight:400,color:"#3D3A37",marginBottom:"28px"},children:"Moje podejście"}),(0,r.jsx)("div",{children:["Łączę wiedzę medyczną z duchową praktyką","Tworzę bezpieczną przestrzeń eksploracji","Wspieram budowanie świadomej relacji z ciałem"].map((e,t)=>(0,r.jsx)("div",{style:{padding:"16px 0",paddingLeft:"24px",borderLeft:"1px solid rgba(196, 165, 117, 0.1)",position:"relative"},children:(0,r.jsx)("span",{style:{fontSize:"14px",fontFamily:"Inter",fontWeight:300,color:"#4A4744"},children:e})},t))})]})]}),(0,r.jsx)("div",{style:o.imageContainer,children:(0,r.jsx)(i.default,{src:"/images/profile/omnie-opt.webp",alt:"Julia Jakubowicz - instruktorka jogi",fill:!0,style:{objectFit:"cover",filter:"grayscale(100%) brightness(1.05)"},sizes:a?"100vw":"45vw",quality:95})})]})}),(0,r.jsx)("div",{style:o.divider}),(0,r.jsx)("section",{style:{maxWidth:"1200px",margin:"0 auto",padding:a?"0 40px":t?"0 60px":"0 80px"},children:(0,r.jsxs)("div",{style:o.gridReverse,children:[(0,r.jsx)("div",{style:o.visualElement,children:(0,r.jsxs)("div",{style:{display:"flex",alignItems:"center",justifyContent:"center",gap:a?"10px":"20px"},children:[(0,r.jsx)("div",{style:{width:a?"40px":"60px",height:a?"40px":"60px",border:"1px solid rgba(196, 165, 117, 0.15)",borderRadius:"50%",transform:a?"translateX(5px)":"translateX(10px)"}}),(0,r.jsx)("div",{style:{width:a?"40px":"60px",height:a?"40px":"60px",border:"1px solid rgba(196, 165, 117, 0.15)",borderRadius:"50%",transform:a?"translateX(-5px)":"translateX(-10px)"}}),(0,r.jsx)("div",{style:{width:a?"40px":"60px",height:a?"40px":"60px",border:"1px solid rgba(196, 165, 117, 0.15)",borderRadius:"50%",transform:a?"translateX(-15px)":"translateX(-30px)"}})]})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{style:o.sectionTitle,children:"Doświadczenie"}),(0,r.jsx)("p",{style:{fontSize:"17px",fontFamily:"Inter",fontWeight:300,color:"#5B5754",lineHeight:"1.8",marginBottom:"32px"},children:"Ponad 8 lat praktyki i nauczania jogi w r\xf3żnych formach i miejscach."}),(0,r.jsx)("div",{style:{fontSize:"15px",fontFamily:"Inter",fontWeight:300,color:"#3D3A37",lineHeight:"1.9",letterSpacing:"0.02em",marginBottom:"56px",textAlign:"left"},children:"Od sceptycznej fizjoterapeutki do przewodniczki duchowych transformacji. Każda podr\xf3ż na Bali i Sri Lanka to lekcja pokory wobec starożytnej mądrości."}),(0,r.jsxs)("div",{style:{marginBottom:"64px"},children:[(0,r.jsx)("h3",{style:{fontSize:"20px",fontFamily:"Cormorant Garamond",fontWeight:400,color:"#3D3A37",marginBottom:"28px"},children:"Kwalifikacje i certyfikaty"}),(0,r.jsx)("div",{children:[{name:"RYT 500 - Certified Yoga Teacher",duration:"Yoga Alliance",note:"Registered Yoga Alliance"},{name:"8 lat praktyki nauczania",duration:"Doświadczenie",note:"Prowadzenie grup i sesji indywidualnych"},{name:"Fizjoterapeutka",duration:"Terapia manualna",note:"Medyczne podejście do jogi"},{name:"200+ uczni\xf3w",duration:"Azja",note:"Retreaty na Bali i Sri Lanka"}].map((e,t)=>(0,r.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"20px 0",borderBottom:3===t?"none":"1px solid rgba(139, 133, 127, 0.15)"},children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{style:{fontSize:"15px",fontFamily:"Inter",fontWeight:400,color:"#3D3A37",margin:"0 0 4px 0"},children:e.name}),(0,r.jsx)("p",{style:{fontSize:"13px",fontFamily:"Inter",fontWeight:300,color:"#8B857F",margin:0},children:e.duration})]}),(0,r.jsx)("span",{style:{fontSize:"13px",fontFamily:"Inter",fontWeight:300,color:"#8B857F"},children:e.note})]},t))})]})]})]})}),(0,r.jsx)(l,{})]})}},96487:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[4447,4693,6533,2697],()=>a(18097));module.exports=r})();