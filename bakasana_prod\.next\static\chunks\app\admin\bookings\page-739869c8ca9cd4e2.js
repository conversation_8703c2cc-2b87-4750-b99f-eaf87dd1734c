(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6907],{47338:(e,t,s)=>{Promise.resolve().then(s.bind(s,71659))},71659:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var a=s(95155),l=s(12115),i=s(35695);function n(){let[e,t]=(0,l.useState)(!1),[s,n]=(0,l.useState)([]),[r,c]=(0,l.useState)(!0),[d,m]=(0,l.useState)("all"),x=(0,i.useRouter)();(0,l.useEffect)(()=>{let e=localStorage.getItem("admin-token");if(!e)return void x.push("/admin");o(e)},[x]);let o=async e=>{try{(await fetch("/api/admin/verify",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${e}`}})).ok?(t(!0),await h()):(localStorage.removeItem("admin-token"),x.push("/admin"))}catch(e){x.push("/admin")}c(!1)},h=async()=>{try{let e=await fetch("/api/admin/bookings");if(e.ok){let t=await e.json();n(t.bookings||[])}}catch(e){}},p=async(e,t)=>{try{let s=localStorage.getItem("admin-token");(await fetch(`/api/admin/bookings/${e}`,{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:`Bearer ${s}`},body:JSON.stringify({status:t})})).ok&&await h()}catch(e){}},u=e=>{switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"confirmed":return"bg-green-100 text-green-800";case"cancelled":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},j=e=>{switch(e){case"pending":return"Oczekuje";case"confirmed":return"Potwierdzona";case"cancelled":return"Anulowana";default:return"Nieznany"}},g=s.filter(e=>"all"===d||e.status===d);return r?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-temple mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-temple",children:"Ładowanie..."})]})}):e?(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-rice to-mist",children:[(0,a.jsx)("header",{className:"bg-white shadow-sm border-b border-temple/10",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("button",{onClick:()=>x.push("/admin"),className:"text-temple hover:text-temple/70 mr-4",children:"← Powr\xf3t"}),(0,a.jsx)("h1",{className:"text-xl font-serif text-temple",children:"Zarządzanie Rezerwacjami"})]}),(0,a.jsx)("div",{className:"flex items-center space-x-4",children:(0,a.jsxs)("span",{className:"text-sm text-wood-light",children:[g.length," rezerwacji"]})})]})})}),(0,a.jsxs)("main",{className:"max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-soft p-6 mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-temple mb-4",children:"Filtry"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:[{key:"all",label:"Wszystkie",count:s.length},{key:"pending",label:"Oczekujące",count:s.filter(e=>"pending"===e.status).length},{key:"confirmed",label:"Potwierdzone",count:s.filter(e=>"confirmed"===e.status).length},{key:"cancelled",label:"Anulowane",count:s.filter(e=>"cancelled"===e.status).length}].map(e=>(0,a.jsxs)("button",{onClick:()=>m(e.key),className:`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${d===e.key?"bg-temple text-white":"bg-temple/10 text-temple hover:bg-temple/20"}`,children:[e.label," (",e.count,")"]},e.key))})]}),(0,a.jsx)("div",{className:"bg-white rounded-xl shadow-soft overflow-hidden",children:0===g.length?(0,a.jsxs)("div",{className:"p-8 text-center",children:[(0,a.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDCC5"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-temple mb-2",children:"Brak rezerwacji"}),(0,a.jsx)("p",{className:"text-wood-light",children:"all"===d?"Nie ma jeszcze żadnych rezerwacji.":`Nie ma rezerwacji o statusie "${j(d)}".`})]}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-temple/10",children:[(0,a.jsx)("thead",{className:"bg-temple/5",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-temple uppercase tracking-wider",children:"Klient"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-temple uppercase tracking-wider",children:"Program"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-temple uppercase tracking-wider",children:"Data"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-temple uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-temple uppercase tracking-wider",children:"Akcje"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-temple/10",children:g.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-temple/5",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"text-sm font-medium text-temple",children:[e.firstName," ",e.lastName]}),(0,a.jsx)("div",{className:"text-sm text-wood-light",children:e.email}),(0,a.jsx)("div",{className:"text-sm text-wood-light",children:e.phone})]})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsx)("div",{className:"text-sm text-temple",children:e.program}),(0,a.jsx)("div",{className:"text-sm text-wood-light",children:e.destination})]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsx)("div",{className:"text-sm text-temple",children:new Date(e.createdAt).toLocaleDateString("pl-PL")}),(0,a.jsx)("div",{className:"text-sm text-wood-light",children:new Date(e.createdAt).toLocaleTimeString("pl-PL")})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${u(e.status)}`,children:j(e.status)})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:["pending"===e.status&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("button",{onClick:()=>p(e.id,"confirmed"),className:"text-green-600 hover:text-green-900",children:"Potwierdź"}),(0,a.jsx)("button",{onClick:()=>p(e.id,"cancelled"),className:"text-red-600 hover:text-red-900",children:"Anuluj"})]}),"confirmed"===e.status&&(0,a.jsx)("button",{onClick:()=>p(e.id,"cancelled"),className:"text-red-600 hover:text-red-900",children:"Anuluj"}),"cancelled"===e.status&&(0,a.jsx)("button",{onClick:()=>p(e.id,"pending"),className:"text-blue-600 hover:text-blue-900",children:"Przywr\xf3ć"})]})})]},e.id))})]})})})]})]}):null}}},e=>{var t=t=>e(e.s=t);e.O(0,[8096,7358],()=>t(47338)),_N_E=e.O()}]);